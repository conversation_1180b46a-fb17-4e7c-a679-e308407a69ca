<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - IndicatorLightControlTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>IndicatorLightControlTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.Bone.BoneSys.html">com.Bone.BoneSys</a> &gt; IndicatorLightControlTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">6</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">19.355s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Method name</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">完整场景测试：从推荐到治疗启动的完整流程</td>
<td class="success">testCompleteIndicatorLightFlow()</td>
<td class="success">5.055s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">场景5：治疗进程启动成功后指示灯保持点亮</td>
<td class="success">testKeepLightOnAfterTreatmentStart()</td>
<td class="success">5.028s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">场景1：治疗头推荐时点亮指示灯</td>
<td class="success">testLightUpOnRecommendation()</td>
<td class="success">3.032s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">场景4：参数下载弹窗取消时关闭指示灯</td>
<td class="success">testTurnOffOnDownloadCancel()</td>
<td class="success">2.033s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">场景2：治疗头选择弹窗关闭时关闭指示灯</td>
<td class="success">testTurnOffOnModalClose()</td>
<td class="success">2.055s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">场景3：治疗头数量不足弹窗关闭时关闭指示灯</td>
<td class="success">testTurnOffOnShortageModalClose()</td>
<td class="success">2.152s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>10:51:23.081 [Test worker] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.Bone.BoneSys.IndicatorLightControlTest]: IndicatorLightControlTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
10:51:23.614 [Test worker] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.Bone.BoneSys.BoneSysApplication for test class com.Bone.BoneSys.IndicatorLightControlTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.7)

2025-08-01 10:51:25 [Test worker] INFO  c.B.B.IndicatorLightControlTest - Starting IndicatorLightControlTest using Java 17.0.15 with PID 8824 (started by admin in D:\Download\VS_data\Bone2\BoneSys)
2025-08-01 10:51:25 [Test worker] DEBUG c.B.B.IndicatorLightControlTest - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-08-01 10:51:25 [Test worker] INFO  c.B.B.IndicatorLightControlTest - The following 1 profile is active: &quot;test&quot;
2025-08-01 10:51:27 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 10:51:28 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 214 ms. Found 8 JPA repository interfaces.
2025-08-01 10:51:36 [Test worker] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 10:51:36 [Test worker] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-01 10:51:36 [Test worker] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-01 10:51:37 [Test worker] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01 10:51:37 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-01 10:51:37 [Test worker] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23365142
2025-08-01 10:51:37 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-01 10:51:37 [Test worker] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01 10:51:38 [Test worker] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.42
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-01 10:51:41 [Test worker] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01 10:51:42 [Test worker] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 10:51:43 [Test worker] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-01 10:51:48 [Test worker] INFO  c.B.B.s.HardwareSimulatorService - Hardware simulator initialized with 20 simulated treatment heads
2025-08-01 10:51:49 [Test worker] INFO  c.B.B.s.TreatmentHeadSyncService - Initializing treatment head data on application startup...
2025-08-01 10:51:54 [Test worker] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting treatment head sync...
2025-08-01 10:51:54 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Syncing all treatment heads from hardware...
2025-08-01 10:51:54 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built query all treatment heads command: TRZI\r\n
2025-08-01 10:51:54 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Parsing TRZI response: null
2025-08-01 10:51:54 [Test worker] ERROR c.B.BoneSys.service.HardwareService - Failed to sync treatment heads from hardware
com.Bone.BoneSys.exception.SerialCommunicationException: Failed to parse TRZI response: null
	at com.Bone.BoneSys.service.HardwareCommandParser.parseQueryAllTreatmentHeadsResponse(HardwareCommandParser.java:193)
	at com.Bone.BoneSys.service.HardwareService.syncAllTreatmentHeads(HardwareService.java:64)
	at com.Bone.BoneSys.service.TreatmentHeadSyncService.performSync(TreatmentHeadSyncService.java:148)
	at com.Bone.BoneSys.service.TreatmentHeadSyncService.initializeOnStartup(TreatmentHeadSyncService.java:81)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:144)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:563)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:144)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:110)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:200)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:139)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:159)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$11(ClassBasedTestDescriptor.java:378)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:383)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$12(ClassBasedTestDescriptor.java:378)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:377)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$7(ClassBasedTestDescriptor.java:290)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:289)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:279)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$6(ClassBasedTestDescriptor.java:278)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$1(TestMethodTestDescriptor.java:105)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:104)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:128)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:128)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
Caused by: java.lang.NullPointerException: Cannot invoke &quot;String.startsWith(String)&quot; because &quot;response&quot; is null
	at com.Bone.BoneSys.service.HardwareCommandParser.parseQueryAllTreatmentHeadsResponse(HardwareCommandParser.java:57)
	... 138 common frames omitted
2025-08-01 10:51:54 [Test worker] WARN  c.B.B.s.TreatmentHeadSyncService - Treatment head sync failed: Hardware communication failed: Hardware sync failed: Failed to parse TRZI response: null
2025-08-01 10:51:54 [Test worker] WARN  c.B.B.s.TreatmentHeadSyncService - Treatment head initialization failed: Hardware communication failed: Hardware sync failed: Failed to parse TRZI response: null
2025-08-01 10:51:54 [Test worker] WARN  c.B.B.s.SerialCommunicationService - No serial ports available
2025-08-01 10:51:54 [Test worker] WARN  c.B.B.s.SerialCommunicationService - Failed to initialize serial communication service: No suitable serial port found
2025-08-01 10:51:54 [Test worker] INFO  c.B.B.s.SerialConnectionManager - Health check started with interval: 30000ms
2025-08-01 10:51:54 [Test worker] INFO  c.B.B.s.SerialConnectionManager - Serial connection manager initialized
2025-08-01 10:51:54 [Test worker] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 10:51:54 [Test worker] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 90059e9d-315f-4144-901e-048bde0bde60

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 10:51:54 [Test worker] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01 10:51:56 [Test worker] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, LogoutFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-08-01 10:51:58 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadReturnDetectionService - 开始检测治疗头归还状态...
2025-08-01 10:51:58 [Test worker] INFO  c.B.B.IndicatorLightControlTest - Started IndicatorLightControlTest in 33.822 seconds (process running for 38.076)
2025-08-01 10:51:58 [Test worker] INFO  c.B.B.service.StartupSyncService - Starting application startup synchronization...
Hibernate: 
    select
        td1_0.id,
        td1_0.body_part,
        td1_0.duration,
        td1_0.frequency,
        td1_0.head_number_used,
        td1_0.intensity,
        td1_0.patch_quantity,
        td1_0.patch_type,
        td1_0.process_id,
        td1_0.status 
    from
        treatment_details td1_0 
    where
        td1_0.status=?
2025-08-01 10:51:58 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadReturnDetectionService - 没有待归还的治疗详情，跳过检测
2025-08-01 10:51:58 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting scheduled treatment head sync...
2025-08-01 10:51:58 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting treatment head sync...
2025-08-01 10:51:58 [scheduling-1] INFO  c.B.BoneSys.service.HardwareService - Syncing all treatment heads from hardware...
2025-08-01 10:51:58 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Built query all treatment heads command: TRZI\r\n
2025-08-01 10:51:58 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Parsing TRZI response: null
2025-08-01 10:51:58 [scheduling-1] ERROR c.B.BoneSys.service.HardwareService - Failed to sync treatment heads from hardware
com.Bone.BoneSys.exception.SerialCommunicationException: Failed to parse TRZI response: null
	at com.Bone.BoneSys.service.HardwareCommandParser.parseQueryAllTreatmentHeadsResponse(HardwareCommandParser.java:193)
	at com.Bone.BoneSys.service.HardwareService.syncAllTreatmentHeads(HardwareService.java:64)
	at com.Bone.BoneSys.service.TreatmentHeadSyncService.performSync(TreatmentHeadSyncService.java:148)
	at com.Bone.BoneSys.service.TreatmentHeadSyncService.scheduledSync(TreatmentHeadSyncService.java:116)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke &quot;String.startsWith(String)&quot; because &quot;response&quot; is null
	at com.Bone.BoneSys.service.HardwareCommandParser.parseQueryAllTreatmentHeadsResponse(HardwareCommandParser.java:57)
	... 19 common frames omitted
2025-08-01 10:51:58 [scheduling-1] WARN  c.B.B.s.TreatmentHeadSyncService - Treatment head sync failed: Hardware communication failed: Hardware sync failed: Failed to parse TRZI response: null
2025-08-01 10:51:58 [scheduling-1] WARN  c.B.B.s.TreatmentHeadSyncService - Scheduled sync failed: Hardware communication failed: Hardware sync failed: Failed to parse TRZI response: null
2025-08-01 10:52:03 [Test worker] INFO  c.B.B.service.StartupSyncService - Synchronizing treatment heads from hardware...
2025-08-01 10:52:03 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Syncing all treatment heads from hardware...
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built query all treatment heads command: TRZI\r\n
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Parsing TRZI response: null
2025-08-01 10:52:03 [Test worker] ERROR c.B.BoneSys.service.HardwareService - Failed to sync treatment heads from hardware
com.Bone.BoneSys.exception.SerialCommunicationException: Failed to parse TRZI response: null
	at com.Bone.BoneSys.service.HardwareCommandParser.parseQueryAllTreatmentHeadsResponse(HardwareCommandParser.java:193)
	at com.Bone.BoneSys.service.HardwareService.syncAllTreatmentHeads(HardwareService.java:64)
	at com.Bone.BoneSys.service.StartupSyncService.run(StartupSyncService.java:33)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:144)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:58)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:46)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1461)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:563)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:144)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:110)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:225)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:152)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:130)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:200)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:139)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:260)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:159)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$11(ClassBasedTestDescriptor.java:378)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:383)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$12(ClassBasedTestDescriptor.java:378)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:377)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$7(ClassBasedTestDescriptor.java:290)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:289)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:279)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$6(ClassBasedTestDescriptor.java:278)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$1(TestMethodTestDescriptor.java:105)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:104)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:128)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:128)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:160)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:146)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:144)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:100)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
Caused by: java.lang.NullPointerException: Cannot invoke &quot;String.startsWith(String)&quot; because &quot;response&quot; is null
	at com.Bone.BoneSys.service.HardwareCommandParser.parseQueryAllTreatmentHeadsResponse(HardwareCommandParser.java:57)
	... 117 common frames omitted
2025-08-01 10:52:03 [Test worker] ERROR c.B.B.service.StartupSyncService - Failed to synchronize treatment heads during startup: Hardware sync failed: Failed to parse TRZI response: null
2025-08-01 10:52:03 [Test worker] WARN  c.B.B.service.StartupSyncService - Application will continue running, but treatment head data may not be up to date
2025-08-01 10:52:03 [Test worker] INFO  c.B.B.service.DatabaseIndexService - 开始检查和创建数据库索引...
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_patients_search_name 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_patients_search_card_id 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_patients_created_at 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] INFO  c.B.B.service.DatabaseIndexService - 患者表索引创建完成
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_records_patient_created 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_records_record_number 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] INFO  c.B.B.service.DatabaseIndexService - 档案表索引创建完成
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_body_part_stats_record 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_body_part_stats_usage_count 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_body_part_stats_body_part 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.DatabaseIndexService - 索引 idx_body_part_stats_patient_lookup 已存在，跳过创建
2025-08-01 10:52:03 [Test worker] INFO  c.B.B.service.DatabaseIndexService - 部位统计表索引创建完成
2025-08-01 10:52:03 [Test worker] INFO  c.B.B.service.DatabaseIndexService - 数据库索引检查和创建完成
=== 指示灯控制逻辑测试 ===
验证指示灯在不同场景下的控制行为
===============================

【场景3】治疗头数量不足弹窗关闭时关闭指示灯
步骤1: 点亮推荐治疗头指示灯
2025-08-01 10:52:03 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Setting treatment head lights for 2 heads
2025-08-01 10:52:03 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built light up command: TWSC02011022\r\n
2025-08-01 10:52:03 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully set lights for 2 treatment heads
步骤2: 模拟用户关闭治疗头数量不足弹窗
2025-08-01 10:52:05 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Turning off lights for 2 treatment heads
2025-08-01 10:52:05 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built turn off light command: TWSN020102\r\n
2025-08-01 10:52:05 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully turned off lights for 2 treatment heads
指示灯关闭结果: 2 个治疗头指示灯已关闭
✅ 场景3测试通过 - 数量不足弹窗关闭时正确关闭指示灯
=== 指示灯控制逻辑测试 ===
验证指示灯在不同场景下的控制行为
===============================

【场景2】治疗头选择弹窗关闭时关闭指示灯
步骤1: 点亮推荐治疗头指示灯
2025-08-01 10:52:05 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Setting treatment head lights for 2 heads
2025-08-01 10:52:05 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built light up command: TWSC02011022\r\n
2025-08-01 10:52:05 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully set lights for 2 treatment heads
步骤2: 模拟用户关闭治疗头选择弹窗
2025-08-01 10:52:07 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Turning off lights for 2 treatment heads
2025-08-01 10:52:07 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built turn off light command: TWSN020102\r\n
2025-08-01 10:52:07 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully turned off lights for 2 treatment heads
指示灯关闭结果: 2 个治疗头指示灯已关闭
✅ 场景2测试通过 - 弹窗关闭时正确关闭指示灯
=== 指示灯控制逻辑测试 ===
验证指示灯在不同场景下的控制行为
===============================

【完整场景测试】从推荐到治疗启动的完整流程

阶段1: 生成治疗头推荐
2025-08-01 10:52:07 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Setting treatment head lights for 2 heads
2025-08-01 10:52:07 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built light up command: TWSC02011022\r\n
2025-08-01 10:52:07 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully set lights for 2 treatment heads
✅ 推荐治疗头指示灯已点亮
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting scheduled treatment head sync...
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting treatment head sync...
2025-08-01 10:52:08 [scheduling-1] INFO  c.B.BoneSys.service.HardwareService - Syncing all treatment heads from hardware...
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Built query all treatment heads command: TRZI\r\n
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Parsing TRZI response: TRZI00

2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Data after removing prefix and terminator: 00
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Parsed head count: 0
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Heads data: , length: 0
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Analyzing TRZI data format - HeadCount: 0, TotalDataLength: 0
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Raw heads data: 
2025-08-01 10:52:08 [scheduling-1] INFO  c.B.BoneSys.service.HardwareService - Successfully synced 0 treatment heads from hardware
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Treatment head sync completed successfully: 0 heads in 1ms
2025-08-01 10:52:08 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Scheduled sync completed successfully: 0 heads processed in 1ms

阶段2: 用户确认选择并启动治疗
   - 用户在治疗头选择弹窗中确认选择
   - 系统发送TWZS指令启动治疗
   - 治疗进程创建成功
   - 页面跳转到治疗进程页面
   - 指示灯保持点亮状态（不发送TWSN指令）
✅ 治疗启动后指示灯保持点亮状态

阶段3: 治疗完成后的清理
   - 治疗完成或用户手动停止
   - 系统发送TWZO指令关闭治疗头
   - 可选：发送TWSN指令关闭指示灯
2025-08-01 10:52:12 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Turning off lights for 2 treatment heads
2025-08-01 10:52:12 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built turn off light command: TWSN020102\r\n
2025-08-01 10:52:12 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully turned off lights for 2 treatment heads
✅ 治疗完成后指示灯已关闭: 2 个

&#x1f389; 完整场景测试通过！
指示灯控制逻辑符合预期：
  ✅ 推荐时点亮
  ✅ 弹窗关闭时关闭
  ✅ 治疗启动后保持点亮
  ✅ 治疗完成后可选关闭
=== 指示灯控制逻辑测试 ===
验证指示灯在不同场景下的控制行为
===============================

【场景1】治疗头推荐时点亮指示灯
步骤1: 发送TWSC指令点亮推荐治疗头指示灯
2025-08-01 10:52:12 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Setting treatment head lights for 3 heads
2025-08-01 10:52:12 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built light up command: TWSC03011022033\r\n
2025-08-01 10:52:12 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully set lights for 2 treatment heads
指示灯点亮结果: 成功
✅ 推荐治疗头指示灯已点亮
   - 治疗头1: 红色指示灯
   - 治疗头2: 绿色指示灯
   - 治疗头3: 蓝色指示灯
✅ 场景1测试通过
=== 指示灯控制逻辑测试 ===
验证指示灯在不同场景下的控制行为
===============================

【场景4】参数下载弹窗取消时关闭指示灯
步骤1: 点亮推荐治疗头指示灯
2025-08-01 10:52:15 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Setting treatment head lights for 2 heads
2025-08-01 10:52:15 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built light up command: TWSC02011022\r\n
2025-08-01 10:52:15 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully set lights for 2 treatment heads
步骤2: 模拟用户取消参数下载弹窗
2025-08-01 10:52:17 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Turning off lights for 2 treatment heads
2025-08-01 10:52:17 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built turn off light command: TWSN020102\r\n
2025-08-01 10:52:17 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully turned off lights for 2 treatment heads
指示灯关闭结果: 2 个治疗头指示灯已关闭
✅ 场景4测试通过 - 参数下载取消时正确关闭指示灯
=== 指示灯控制逻辑测试 ===
验证指示灯在不同场景下的控制行为
===============================

【场景5】治疗进程启动成功后指示灯保持点亮
步骤1: 点亮推荐治疗头指示灯
2025-08-01 10:52:17 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Setting treatment head lights for 2 heads
2025-08-01 10:52:17 [Test worker] DEBUG c.B.B.service.HardwareCommandParser - Built light up command: TWSC02011022\r\n
2025-08-01 10:52:17 [Test worker] INFO  c.B.BoneSys.service.HardwareService - Successfully set lights for 2 treatment heads
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting scheduled treatment head sync...
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Starting treatment head sync...
2025-08-01 10:52:18 [scheduling-1] INFO  c.B.BoneSys.service.HardwareService - Syncing all treatment heads from hardware...
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Built query all treatment heads command: TRZI\r\n
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Parsing TRZI response: TRZI00

2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Data after removing prefix and terminator: 00
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Parsed head count: 0
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Heads data: , length: 0
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Analyzing TRZI data format - HeadCount: 0, TotalDataLength: 0
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.service.HardwareCommandParser - Raw heads data: 
2025-08-01 10:52:18 [scheduling-1] INFO  c.B.BoneSys.service.HardwareService - Successfully synced 0 treatment heads from hardware
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Treatment head sync completed successfully: 0 heads in 2ms
2025-08-01 10:52:18 [scheduling-1] DEBUG c.B.B.s.TreatmentHeadSyncService - Scheduled sync completed successfully: 0 heads processed in 2ms
步骤2: 模拟治疗进程启动成功
   - 发送TWZS指令启动治疗
   - 治疗进程创建成功
   - 页面跳转到治疗进程页面
步骤3: 验证指示灯保持点亮状态
   ✅ 指示灯保持点亮，不发送TWSN关闭指令
   ✅ 指示灯将在治疗过程中持续显示治疗状态
✅ 场景5测试通过 - 治疗启动后指示灯正确保持点亮
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.3</a> at 2025年8月1日 上午10:52:22</p>
</div>
</div>
</body>
</html>

package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 硬件服务 - 完整实现版
 * 使用WebSocket与硬件设备通信
 */
@Service
public class HardwareService {

    private static final Logger logger = LoggerFactory.getLogger(HardwareService.class);

    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;

    @Autowired
    private WebSocketHardwareService webSocketHardwareService;

    @Autowired
    private HardwareCommandParser commandParser;
    
    public List<TreatmentHeadInfo> getAllTreatmentHeadsForManagement() {
        List<TreatmentHead> allDbHeads = treatmentHeadRepository.findAll();
        return allDbHeads.stream()
            .map(this::convertToTreatmentHeadInfo)
            .sorted((h1, h2) -> Integer.compare(h1.getHeadNumber(), h2.getHeadNumber()))
            .collect(Collectors.toList());
    }
    
    private TreatmentHeadInfo convertToTreatmentHeadInfo(TreatmentHead head) {
        TreatmentHeadInfo info = new TreatmentHeadInfo();
        info.setHeadNumber(head.getHeadNumber());
        info.setBatteryLevel(head.getBatteryLevel() != null ? head.getBatteryLevel() : 0);
        info.setUsageCount(head.getTotalUsageCount());
        info.setSlotNumber(head.getSlotNumber() != null ? head.getSlotNumber() : 0);
        info.setStatus(head.getRealtimeStatus().name());
        info.setCompartmentType(head.getCompartmentType());
        return info;
    }
    
    public List<TreatmentHeadInfo> syncAllTreatmentHeads() throws SerialCommunicationException {
        try {
            logger.info("Syncing all treatment heads from hardware...");

            // 构建TRZI查询指令
            String command = commandParser.buildQueryAllTreatmentHeadsCommand();

            // 发送指令到硬件
            String response = webSocketHardwareService.sendCommand(command);

            // 解析硬件响应
            List<TreatmentHeadInfo> hardwareHeads = commandParser.parseQueryAllTreatmentHeadsResponse(response);

            logger.info("Successfully synced {} treatment heads from hardware", hardwareHeads.size());
            return hardwareHeads;

        } catch (Exception e) {
            logger.error("Failed to sync treatment heads from hardware", e);
            throw new SerialCommunicationException("Hardware sync failed: " + e.getMessage(), e);
        }
    }

    public boolean isHardwareConnected() {
        return webSocketHardwareService.isConnected();
    }

    public List<TreatmentHeadLightResponse> setTreatmentHeadLights(List<TreatmentHeadLightRequest> lightRequests)
            throws SerialCommunicationException {
        try {
            logger.info("Setting treatment head lights for {} heads", lightRequests.size());

            // 构建TWSC指令
            String command = commandParser.buildLightUpCommand(lightRequests);

            // 发送指令到硬件
            String response = webSocketHardwareService.sendCommand(command);

            // 解析硬件响应
            List<TreatmentHeadLightResponse> lightResponses = commandParser.parseLightUpResponse(response);

            logger.info("Successfully set lights for {} treatment heads", lightResponses.size());
            return lightResponses;

        } catch (Exception e) {
            logger.error("Failed to set treatment head lights", e);
            throw new SerialCommunicationException("Light control failed: " + e.getMessage(), e);
        }
    }

    /**
     * 点亮治疗头指示灯（别名方法，用于向后兼容）
     */
    public List<TreatmentHeadLightResponse> lightUpTreatmentHeads(List<TreatmentHeadLightRequest> lightRequests)
            throws SerialCommunicationException {
        return setTreatmentHeadLights(lightRequests);
    }

    public List<Integer> turnOffTreatmentHeadLights(List<Integer> headNumbers) throws SerialCommunicationException {
        try {
            logger.info("Turning off lights for {} treatment heads", headNumbers.size());

            // 构建TWSN指令
            String command = commandParser.buildTurnOffLightCommand(headNumbers);

            // 发送指令到硬件
            String response = webSocketHardwareService.sendCommand(command);

            // 解析硬件响应
            List<Integer> turnedOffHeads = commandParser.parseTurnOffLightResponse(response);

            logger.info("Successfully turned off lights for {} treatment heads", turnedOffHeads.size());
            return turnedOffHeads;

        } catch (Exception e) {
            logger.error("Failed to turn off treatment head lights", e);
            throw new SerialCommunicationException("Light turn off failed: " + e.getMessage(), e);
        }
    }

    public boolean sendTreatmentParams(TreatmentParamsRequest request) throws SerialCommunicationException {
        try {
            logger.info("Sending treatment parameters to hardware");

            // 构建TWSDT指令
            String command = commandParser.buildSendTreatmentParamsCommand(request);

            // 发送指令到硬件
            String response = webSocketHardwareService.sendCommand(command);

            // 验证响应
            boolean success = commandParser.validateSendTreatmentParamsResponse(response, request);

            logger.info("Treatment parameters sent: {}", success ? "success" : "failed");
            return success;

        } catch (Exception e) {
            logger.error("Failed to send treatment parameters", e);
            throw new SerialCommunicationException("Parameter sending failed: " + e.getMessage(), e);
        }
    }

    public boolean startTreatment(int headNumber, int duration, int intensity, int frequency)
            throws SerialCommunicationException {
        try {
            logger.info("Starting treatment on head {} with duration={}, intensity={}, frequency={}",
                       headNumber, duration, intensity, frequency);

            // 构建TWS指令
            String command = commandParser.buildStartTreatmentCommand(headNumber, duration, intensity, frequency);

            // 发送指令到硬件
            String response = webSocketHardwareService.sendCommand(command);

            // 验证响应
            boolean success = commandParser.validateStartTreatmentResponse(response, headNumber, duration, intensity, frequency);

            logger.info("Treatment start on head {}: {}", headNumber, success ? "success" : "failed");
            return success;

        } catch (Exception e) {
            logger.error("Failed to start treatment on head {}", headNumber, e);
            throw new SerialCommunicationException("Treatment start failed: " + e.getMessage(), e);
        }
    }

    public boolean stopTreatment(int headNumber) throws SerialCommunicationException {
        try {
            logger.info("Stopping treatment on head {}", headNumber);

            // 构建TWZO指令
            String command = commandParser.buildStopTreatmentCommand(headNumber);

            // 发送指令到硬件
            String response = webSocketHardwareService.sendCommand(command);

            // 解析响应并验证
            int responseHeadNumber = commandParser.parseStopTreatmentResponse(response);
            boolean success = responseHeadNumber == headNumber;

            logger.info("Treatment stop on head {}: {}", headNumber, success ? "success" : "failed");
            return success;

        } catch (Exception e) {
            logger.error("Failed to stop treatment on head {}", headNumber, e);
            throw new SerialCommunicationException("Treatment stop failed: " + e.getMessage(), e);
        }
    }

    public String getHardwareInfo() {
        try {
            boolean connected = webSocketHardwareService.isConnected();
            String connectionStatus = connected ? "已连接" : "未连接";
            return String.format("WebSocket硬件服务 - %s", connectionStatus);
        } catch (Exception e) {
            return "WebSocket硬件服务 - 状态未知";
        }
    }

    public boolean testHardwareConnection() {
        try {
            // 尝试发送一个简单的查询指令来测试连接
            String command = commandParser.buildQueryAllTreatmentHeadsCommand();
            String response = webSocketHardwareService.sendCommand(command);
            return response != null && !response.isEmpty();
        } catch (Exception e) {
            logger.warn("Hardware connection test failed", e);
            return false;
        }
    }

    public void reconnectHardware() throws SerialCommunicationException {
        try {
            logger.info("Reconnecting to WebSocket hardware server...");
            webSocketHardwareService.disconnect();
            webSocketHardwareService.connect();
            logger.info("Hardware reconnection completed");
        } catch (Exception e) {
            logger.error("Failed to reconnect to hardware", e);
            throw new SerialCommunicationException("Hardware reconnection failed: " + e.getMessage(), e);
        }
    }
}
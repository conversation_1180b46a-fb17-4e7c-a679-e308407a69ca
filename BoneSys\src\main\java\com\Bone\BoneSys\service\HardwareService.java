package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 硬件服务 - 简化版
 */
@Service
public class HardwareService {
    
    private static final Logger logger = LoggerFactory.getLogger(HardwareService.class);
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    public List<TreatmentHeadInfo> getAllTreatmentHeadsForManagement() {
        List<TreatmentHead> allDbHeads = treatmentHeadRepository.findAll();
        return allDbHeads.stream()
            .map(this::convertToTreatmentHeadInfo)
            .sorted((h1, h2) -> Integer.compare(h1.getHeadNumber(), h2.getHeadNumber()))
            .collect(Collectors.toList());
    }
    
    private TreatmentHeadInfo convertToTreatmentHeadInfo(TreatmentHead head) {
        TreatmentHeadInfo info = new TreatmentHeadInfo();
        info.setHeadNumber(head.getHeadNumber());
        info.setBatteryLevel(head.getBatteryLevel() != null ? head.getBatteryLevel() : 0);
        info.setUsageCount(head.getTotalUsageCount());
        info.setSlotNumber(head.getSlotNumber() != null ? head.getSlotNumber() : 0);
        info.setStatus(head.getRealtimeStatus().name());
        info.setCompartmentType(head.getCompartmentType());
        return info;
    }
    
    public List<TreatmentHeadInfo> syncAllTreatmentHeads() throws SerialCommunicationException {
        return getAllTreatmentHeadsForManagement();
    }
    
    public boolean isHardwareConnected() {
        return true;
    }
    
    public List<TreatmentHeadLightResponse> setTreatmentHeadLights(List<TreatmentHeadLightRequest> lightRequests) 
            throws SerialCommunicationException {
        return java.util.Collections.emptyList(); // 简化实现
    }
    
    public List<Integer> turnOffTreatmentHeadLights(List<Integer> headNumbers) throws SerialCommunicationException {
        return headNumbers;
    }
    
    public boolean sendTreatmentParams(TreatmentParamsRequest request) throws SerialCommunicationException {
        return true;
    }
    
    public boolean startTreatment(int headNumber, int duration, int intensity, int frequency) 
            throws SerialCommunicationException {
        return true;
    }
    
    public boolean stopTreatment(int headNumber) throws SerialCommunicationException {
        return true;
    }
    
    public String getHardwareInfo() {
        return "硬件服务 - 简化版";
    }
    
    public boolean testHardwareConnection() {
        return true;
    }
    
    public void reconnectHardware() throws SerialCommunicationException {
        logger.info("Reconnecting to hardware - simplified implementation");
    }
}
@echo off
echo ========================================
echo FREEBONE医疗系统 - 前端启动脚本
echo ========================================
echo.

echo 检查Node.js环境...
node --version
if %ERRORLEVEL% neq 0 (
    echo ❌ Node.js未安装或不在PATH中
    echo 请安装Node.js 16+版本
    pause
    exit /b 1
)

echo.
echo 检查npm环境...
npm --version
if %ERRORLEVEL% neq 0 (
    echo ❌ npm未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
npm install
if %ERRORLEVEL% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo ✅ 环境检查完成
echo.
echo 启动前端开发服务器...
echo 前端地址：http://localhost:5173
echo 后端地址：http://localhost:8080
echo.
echo 按Ctrl+C停止服务
echo.

npm run dev

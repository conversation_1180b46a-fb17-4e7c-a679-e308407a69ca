import { TdDatePickerPanelProps } from '../date-picker/type';
import { PropType } from 'vue';
declare const _default: {
    defaultTime: {
        type: StringConstructor;
        default: string;
    };
    onCellClick: PropType<TdDatePickerPanelProps["onCellClick"]>;
    onChange: PropType<TdDatePickerPanelProps["onChange"]>;
    onConfirm: PropType<TdDatePickerPanelProps["onConfirm"]>;
    onMonthChange: PropType<TdDatePickerPanelProps["onMonthChange"]>;
    onPanelClick: PropType<TdDatePickerPanelProps["onPanelClick"]>;
    onPresetClick: PropType<TdDatePickerPanelProps["onPresetClick"]>;
    onTimeChange: PropType<TdDatePickerPanelProps["onTimeChange"]>;
    onYearChange: PropType<TdDatePickerPanelProps["onYearChange"]>;
};
export default _default;

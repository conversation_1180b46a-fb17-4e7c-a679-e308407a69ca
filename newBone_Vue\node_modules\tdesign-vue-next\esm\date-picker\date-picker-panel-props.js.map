{"version": 3, "file": "date-picker-panel-props.js", "sources": ["../../../components/date-picker/date-picker-panel-props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdDatePickerPanelProps } from '../date-picker/type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 时间选择器默认值，当 value/defaultValue 未设置值时有效 */\n  defaultTime: {\n    type: String,\n    default: '00:00:00',\n  },\n  /** 点击日期单元格时触发 */\n  onCellClick: Function as PropType<TdDatePickerPanelProps['onCellClick']>,\n  /** 选中值发生变化时触发。参数 `context.trigger` 表示触发当前事件的来源，不同的模式触发来源也会不同 */\n  onChange: Function as PropType<TdDatePickerPanelProps['onChange']>,\n  /** 如果存在“确定”按钮，则点击“确定”按钮时触发 */\n  onConfirm: Function as PropType<TdDatePickerPanelProps['onConfirm']>,\n  /** 月份切换发生变化时触发 */\n  onMonthChange: Function as PropType<TdDatePickerPanelProps['onMonthChange']>,\n  /** 点击面板时触发 */\n  onPanelClick: Function as PropType<TdDatePickerPanelProps['onPanelClick']>,\n  /** 点击预设按钮后触发 */\n  onPresetClick: Function as PropType<TdDatePickerPanelProps['onPresetClick']>,\n  /** 时间切换发生变化时触发 */\n  onTimeChange: Function as PropType<TdDatePickerPanelProps['onTimeChange']>,\n  /** 年份切换发生变化时触发 */\n  onYearChange: Function as PropType<TdDatePickerPanelProps['onYearChange']>,\n};\n"], "names": ["defaultTime", "type", "String", "onCellClick", "Function", "onChange", "onConfirm", "onMonthChange", "onPanelClick", "onPresetClick", "onTimeChange", "onYearChange"], "mappings": ";;;;;;AASA,2BAAe;AAEbA,EAAAA,WAAa,EAAA;AACXC,IAAAA,IAAM,EAAAC,MAAA;IACN,SAAS,EAAA,UAAA;GACX;AAEAC,EAAAA,WAAa,EAAAC,QAAA;AAEbC,EAAAA,QAAU,EAAAD,QAAA;AAEVE,EAAAA,SAAW,EAAAF,QAAA;AAEXG,EAAAA,aAAe,EAAAH,QAAA;AAEfI,EAAAA,YAAc,EAAAJ,QAAA;AAEdK,EAAAA,aAAe,EAAAL,QAAA;AAEfM,EAAAA,YAAc,EAAAN,QAAA;AAEdO,EAAAA,YAAc,EAAAP,QAAAA;AAChB,CAAA;;;;"}
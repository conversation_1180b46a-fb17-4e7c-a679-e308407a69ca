import { TdDateRangePickerPanelProps } from '../date-picker/type';
import { PropType } from 'vue';
declare const _default: {
    defaultTime: {
        type: PropType<TdDateRangePickerPanelProps["defaultTime"]>;
        default: () => TdDateRangePickerPanelProps["defaultTime"];
    };
    onCellClick: PropType<TdDateRangePickerPanelProps["onCellClick"]>;
    onChange: PropType<TdDateRangePickerPanelProps["onChange"]>;
    onConfirm: PropType<TdDateRangePickerPanelProps["onConfirm"]>;
    onMonthChange: PropType<TdDateRangePickerPanelProps["onMonthChange"]>;
    onPanelClick: PropType<TdDateRangePickerPanelProps["onPanelClick"]>;
    onPresetClick: PropType<TdDateRangePickerPanelProps["onPresetClick"]>;
    onTimeChange: PropType<TdDateRangePickerPanelProps["onTimeChange"]>;
    onYearChange: PropType<TdDateRangePickerPanelProps["onYearChange"]>;
};
export default _default;

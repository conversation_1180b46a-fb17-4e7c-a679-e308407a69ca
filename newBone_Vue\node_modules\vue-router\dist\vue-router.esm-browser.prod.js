/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */
import{inject,onUnmounted,onDeactivated,onActivated,computed,unref,defineComponent,reactive,h,provide,ref,watch,shallowRef,shallowReactive,nextTick}from"vue";const isBrowser="undefined"!=typeof document;function isRouteComponent(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function isESModule(e){return e.__esModule||"Module"===e[Symbol.toStringTag]||e.default&&isRouteComponent(e.default)}const assign=Object.assign;function applyToParams(e,t){const n={};for(const o in t){const r=t[o];n[o]=isArray(r)?r.map(e):e(r)}return n}const noop=()=>{},isArray=Array.isArray,HASH_RE=/#/g,AMPERSAND_RE=/&/g,SLASH_RE=/\//g,EQUAL_RE=/=/g,IM_RE=/\?/g,PLUS_RE=/\+/g,ENC_BRACKET_OPEN_RE=/%5B/g,ENC_BRACKET_CLOSE_RE=/%5D/g,ENC_CARET_RE=/%5E/g,ENC_BACKTICK_RE=/%60/g,ENC_CURLY_OPEN_RE=/%7B/g,ENC_PIPE_RE=/%7C/g,ENC_CURLY_CLOSE_RE=/%7D/g,ENC_SPACE_RE=/%20/g;function commonEncode(e){return encodeURI(""+e).replace(ENC_PIPE_RE,"|").replace(ENC_BRACKET_OPEN_RE,"[").replace(ENC_BRACKET_CLOSE_RE,"]")}function encodeHash(e){return commonEncode(e).replace(ENC_CURLY_OPEN_RE,"{").replace(ENC_CURLY_CLOSE_RE,"}").replace(ENC_CARET_RE,"^")}function encodeQueryValue(e){return commonEncode(e).replace(PLUS_RE,"%2B").replace(ENC_SPACE_RE,"+").replace(HASH_RE,"%23").replace(AMPERSAND_RE,"%26").replace(ENC_BACKTICK_RE,"`").replace(ENC_CURLY_OPEN_RE,"{").replace(ENC_CURLY_CLOSE_RE,"}").replace(ENC_CARET_RE,"^")}function encodeQueryKey(e){return encodeQueryValue(e).replace(EQUAL_RE,"%3D")}function encodePath(e){return commonEncode(e).replace(HASH_RE,"%23").replace(IM_RE,"%3F")}function encodeParam(e){return null==e?"":encodePath(e).replace(SLASH_RE,"%2F")}function decode(e){try{return decodeURIComponent(""+e)}catch(e){}return""+e}const TRAILING_SLASH_RE=/\/$/,removeTrailingSlash=e=>e.replace(TRAILING_SLASH_RE,"");function parseURL(e,t,n="/"){let o,r={},a="",i="";const s=t.indexOf("#");let c=t.indexOf("?");return s<c&&s>=0&&(c=-1),c>-1&&(o=t.slice(0,c),a=t.slice(c+1,s>-1?s:t.length),r=e(a)),s>-1&&(o=o||t.slice(0,s),i=t.slice(s,t.length)),o=resolveRelativePath(null!=o?o:t,n),{fullPath:o+(a&&"?")+a+i,path:o,query:r,hash:decode(i)}}function stringifyURL(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function stripBase(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function isSameRouteLocation(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&isSameRouteRecord(t.matched[o],n.matched[r])&&isSameRouteLocationParams(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function isSameRouteRecord(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function isSameRouteLocationParams(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!isSameRouteLocationParamsValue(e[n],t[n]))return!1;return!0}function isSameRouteLocationParamsValue(e,t){return isArray(e)?isEquivalentArray(e,t):isArray(t)?isEquivalentArray(t,e):e===t}function isEquivalentArray(e,t){return isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function resolveRelativePath(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let a,i,s=n.length-1;for(a=0;a<o.length;a++)if(i=o[a],"."!==i){if(".."!==i)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+o.slice(a).join("/")}const START_LOCATION_NORMALIZED={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var NavigationType,NavigationDirection;!function(e){e.pop="pop",e.push="push"}(NavigationType||(NavigationType={})),function(e){e.back="back",e.forward="forward",e.unknown=""}(NavigationDirection||(NavigationDirection={}));const START="";function normalizeBase(e){if(!e)if(isBrowser){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),removeTrailingSlash(e)}const BEFORE_HASH_RE=/^[^#]+#/;function createHref(e,t){return e.replace(BEFORE_HASH_RE,"#")+t}function getElementPosition(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}const computeScrollPosition=()=>({left:window.scrollX,top:window.scrollY});function scrollToPosition(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=getElementPosition(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function getScrollKey(e,t){return(history.state?history.state.position-t:-1)+e}const scrollPositions=new Map;function saveScrollPosition(e,t){scrollPositions.set(e,t)}function getSavedScrollPosition(e){const t=scrollPositions.get(e);return scrollPositions.delete(e),t}let createBaseLocation=()=>location.protocol+"//"+location.host;function createCurrentLocation(e,t){const{pathname:n,search:o,hash:r}=t,a=e.indexOf("#");if(a>-1){let t=r.includes(e.slice(a))?e.slice(a).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),stripBase(n,"")}return stripBase(n,e)+o+r}function useHistoryListeners(e,t,n,o){let r=[],a=[],i=null;const s=({state:a})=>{const s=createCurrentLocation(e,location),c=n.value,u=t.value;let l=0;if(a){if(n.value=s,t.value=a,i&&i===c)return void(i=null);l=u?a.position-u.position:0}else o(s);r.forEach((e=>{e(n.value,c,{delta:l,type:NavigationType.pop,direction:l?l>0?NavigationDirection.forward:NavigationDirection.back:NavigationDirection.unknown})}))};function c(){const{history:e}=window;e.state&&e.replaceState(assign({},e.state,{scroll:computeScrollPosition()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:function(){i=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return a.push(t),t},destroy:function(){for(const e of a)e();a=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",c)}}}function buildState(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?computeScrollPosition():null}}function useHistoryStateNavigation(e){const{history:t,location:n}=window,o={value:createCurrentLocation(e,n)},r={value:t.state};function a(o,a,i){const s=e.indexOf("#"),c=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+o:createBaseLocation()+e+o;try{t[i?"replaceState":"pushState"](a,"",c),r.value=a}catch(e){console.error(e),n[i?"replace":"assign"](c)}}return r.value||a(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const i=assign({},r.value,t.state,{forward:e,scroll:computeScrollPosition()});a(i.current,i,!0),a(e,assign({},buildState(o.value,e,null),{position:i.position+1},n),!1),o.value=e},replace:function(e,n){a(e,assign({},t.state,buildState(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function createWebHistory(e){const t=useHistoryStateNavigation(e=normalizeBase(e)),n=useHistoryListeners(e,t.state,t.location,t.replace);const o=assign({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:createHref.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function createMemoryHistory(e=""){let t=[],n=[["",{}]],o=0;function r(e,t={}){o++,o!==n.length&&n.splice(o),n.push([e,t])}const a={location:"",state:{},base:e=normalizeBase(e),createHref:createHref.bind(null,e),replace(e,t){n.splice(o--,1),r(e,t)},push(e,t){r(e,t)},listen:e=>(t.push(e),()=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)}),destroy(){t=[],n=[["",{}]],o=0},go(e,r=!0){const a=this.location,i=e<0?NavigationDirection.back:NavigationDirection.forward;o=Math.max(0,Math.min(o+e,n.length-1)),r&&function(e,n,{direction:o,delta:r}){const a={direction:o,delta:r,type:NavigationType.pop};for(const o of t)o(e,n,a)}(this.location,a,{direction:i,delta:e})}};return Object.defineProperty(a,"location",{enumerable:!0,get:()=>n[o][0]}),Object.defineProperty(a,"state",{enumerable:!0,get:()=>n[o][1]}),a}function createWebHashHistory(e){return(e=location.host?e||location.pathname+location.search:"").includes("#")||(e+="#"),createWebHistory(e)}function isRouteLocation(e){return"string"==typeof e||e&&"object"==typeof e}function isRouteName(e){return"string"==typeof e||"symbol"==typeof e}const NavigationFailureSymbol=Symbol("");var NavigationFailureType;function createRouterError(e,t){return assign(new Error,{type:e,[NavigationFailureSymbol]:!0},t)}function isNavigationFailure(e,t){return e instanceof Error&&NavigationFailureSymbol in e&&(null==t||!!(e.type&t))}!function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"}(NavigationFailureType||(NavigationFailureType={}));const BASE_PARAM_PATTERN="[^/]+?",BASE_PATH_PARSER_OPTIONS={sensitive:!1,strict:!1,start:!0,end:!0},REGEX_CHARS_RE=/[.+*?^${}()[\]/\\]/g;function tokensToParser(e,t){const n=assign({},BASE_PATH_PARSER_OPTIONS,t),o=[];let r=n.start?"^":"";const a=[];for(const t of e){const e=t.length?[]:[90];n.strict&&!t.length&&(r+="/");for(let o=0;o<t.length;o++){const i=t[o];let s=40+(n.sensitive?.25:0);if(0===i.type)o||(r+="/"),r+=i.value.replace(REGEX_CHARS_RE,"\\$&"),s+=40;else if(1===i.type){const{value:e,repeatable:n,optional:c,regexp:u}=i;a.push({name:e,repeatable:n,optional:c});const l=u||BASE_PARAM_PATTERN;if(l!==BASE_PARAM_PATTERN){s+=10;try{new RegExp(`(${l})`)}catch(t){throw new Error(`Invalid custom RegExp for param "${e}" (${l}): `+t.message)}}let f=n?`((?:${l})(?:/(?:${l}))*)`:`(${l})`;o||(f=c&&t.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===l&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");return{re:i,score:o,keys:a,parse:function(e){const t=e.match(i),n={};if(!t)return null;for(let e=1;e<t.length;e++){const o=t[e]||"",r=a[e-1];n[r.name]=o&&r.repeatable?o.split("/"):o}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:a,repeatable:i,optional:s}=e,c=a in t?t[a]:"";if(isArray(c)&&!i)throw new Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);const u=isArray(c)?c.join("/"):c;if(!u){if(!s)throw new Error(`Missing required param "${a}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=u}}return n||"/"}}}function compareScoreArray(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function comparePathParserScore(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=compareScoreArray(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(isLastScoreNegative(o))return 1;if(isLastScoreNegative(r))return-1}return r.length-o.length}function isLastScoreNegative(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ROOT_TOKEN={type:0,value:""},VALID_PARAM_RE=/[a-zA-Z0-9_]/;function tokenizePath(e){if(!e)return[[]];if("/"===e)return[[ROOT_TOKEN]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${u}": ${e}`)}let n=0,o=n;const r=[];let a;function i(){a&&r.push(a),a=[]}let s,c=0,u="",l="";function f(){u&&(0===n?a.push({type:0,value:u}):1===n||2===n||3===n?(a.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:u,regexp:l,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),u="")}function p(){u+=s}for(;c<e.length;)if(s=e[c++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(u&&f(),i()):":"===s?(f(),n=1):p();break;case 4:p(),n=o;break;case 1:"("===s?n=2:VALID_PARAM_RE.test(s)?p():(f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&c--);break;case 2:")"===s?"\\"==l[l.length-1]?l=l.slice(0,-1)+s:n=3:l+=s;break;case 3:f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&c--,l="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function createRouteRecordMatcher(e,t,n){const o=tokensToParser(tokenizePath(e.path),n),r=assign(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function createRouterMatcher(e,t){const n=[],o=new Map;function r(e,n,o){const s=!o,c=normalizeRouteRecord(e);c.aliasOf=o&&o.record;const u=mergeOptions(t,e),l=[c];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)l.push(normalizeRouteRecord(assign({},c,{components:o?o.record.components:c.components,path:e,aliasOf:o?o.record:c})))}let f,p;for(const t of l){const{path:l}=t;if(n&&"/"!==l[0]){const e=n.record.path;t.path=n.record.path+(l&&("/"===e[e.length-1]?"":"/")+l)}if(f=createRouteRecordMatcher(t,n,u),o?o.alias.push(f):(p=p||f,p!==f&&p.alias.push(f),s&&e.name&&!isAliasRecord(f)&&a(e.name)),isMatchable(f)&&i(f),c.children){const e=c.children;for(let t=0;t<e.length;t++)r(e[t],f,o&&o.children[t])}o=o||f}return p?()=>{a(p)}:noop}function a(e){if(isRouteName(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(a),t.alias.forEach(a))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(a),e.alias.forEach(a))}}function i(e){const t=findInsertionIndex(e,n);n.splice(t,0,e),e.record.name&&!isAliasRecord(e)&&o.set(e.record.name,e)}return t=mergeOptions({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,a,i,s={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw createRouterError(1,{location:e});i=r.record.name,s=assign(paramsFromLocation(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&paramsFromLocation(e.params,r.keys.map((e=>e.name)))),a=r.stringify(s)}else if(null!=e.path)a=e.path,r=n.find((e=>e.re.test(a))),r&&(s=r.parse(a),i=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw createRouterError(1,{location:e,currentLocation:t});i=r.record.name,s=assign({},t.params,e.params),a=r.stringify(s)}const c=[];let u=r;for(;u;)c.unshift(u.record),u=u.parent;return{name:i,path:a,params:s,matched:c,meta:mergeMetaFields(c)}},removeRoute:a,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function paramsFromLocation(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function normalizeRouteRecord(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:normalizeRecordProps(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function normalizeRecordProps(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function isAliasRecord(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function mergeMetaFields(e){return e.reduce(((e,t)=>assign(e,t.meta)),{})}function mergeOptions(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function findInsertionIndex(e,t){let n=0,o=t.length;for(;n!==o;){const r=n+o>>1;comparePathParserScore(e,t[r])<0?o=r:n=r+1}const r=getInsertionAncestor(e);return r&&(o=t.lastIndexOf(r,o-1)),o}function getInsertionAncestor(e){let t=e;for(;t=t.parent;)if(isMatchable(t)&&0===comparePathParserScore(e,t))return t}function isMatchable({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function parseQuery(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let e=0;e<n.length;++e){const o=n[e].replace(PLUS_RE," "),r=o.indexOf("="),a=decode(r<0?o:o.slice(0,r)),i=r<0?null:decode(o.slice(r+1));if(a in t){let e=t[a];isArray(e)||(e=t[a]=[e]),e.push(i)}else t[a]=i}return t}function stringifyQuery(e){let t="";for(let n in e){const o=e[n];if(n=encodeQueryKey(n),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(isArray(o)?o.map((e=>e&&encodeQueryValue(e))):[o&&encodeQueryValue(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function normalizeQuery(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=isArray(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const matchedRouteKey=Symbol(""),viewDepthKey=Symbol(""),routerKey=Symbol(""),routeLocationKey=Symbol(""),routerViewLocationKey=Symbol("");function useCallbacks(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function registerGuard(e,t,n){const o=()=>{e[t].delete(n)};onUnmounted(o),onDeactivated(o),onActivated((()=>{e[t].add(n)})),e[t].add(n)}function onBeforeRouteLeave(e){const t=inject(matchedRouteKey,{}).value;t&&registerGuard(t,"leaveGuards",e)}function onBeforeRouteUpdate(e){const t=inject(matchedRouteKey,{}).value;t&&registerGuard(t,"updateGuards",e)}function guardToPromiseFn(e,t,n,o,r,a=e=>e()){const i=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((s,c)=>{const u=e=>{!1===e?c(createRouterError(4,{from:n,to:t})):e instanceof Error?c(e):isRouteLocation(e)?c(createRouterError(2,{from:t,to:e})):(i&&o.enterCallbacks[r]===i&&"function"==typeof e&&i.push(e),s())},l=a((()=>e.call(o&&o.instances[r],t,n,u)));let f=Promise.resolve(l);e.length<3&&(f=f.then(u)),f.catch((e=>c(e)))}))}function extractComponentsGuards(e,t,n,o,r=e=>e()){const a=[];for(const i of e)for(const e in i.components){let s=i.components[e];if("beforeRouteEnter"===t||i.instances[e])if(isRouteComponent(s)){const c=(s.__vccOpts||s)[t];c&&a.push(guardToPromiseFn(c,n,o,i,e,r))}else{let c=s();a.push((()=>c.then((a=>{if(!a)throw new Error(`Couldn't resolve component "${e}" at "${i.path}"`);const s=isESModule(a)?a.default:a;i.mods[e]=a,i.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&guardToPromiseFn(c,n,o,i,e,r)()}))))}}return a}function loadRouteLocation(e){return e.matched.every((e=>e.redirect))?Promise.reject(new Error("Cannot load a route that redirects.")):Promise.all(e.matched.map((e=>e.components&&Promise.all(Object.keys(e.components).reduce(((t,n)=>{const o=e.components[n];return"function"!=typeof o||"displayName"in o||t.push(o().then((t=>{if(!t)return Promise.reject(new Error(`Couldn't resolve component "${n}" at "${e.path}". Ensure you passed a function that returns a promise.`));const o=isESModule(t)?t.default:t;e.mods[n]=t,e.components[n]=o}))),t}),[]))))).then((()=>e))}function useLink(e){const t=inject(routerKey),n=inject(routeLocationKey),o=computed((()=>{const n=unref(e.to);return t.resolve(n)})),r=computed((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],a=n.matched;if(!r||!a.length)return-1;const i=a.findIndex(isSameRouteRecord.bind(null,r));if(i>-1)return i;const s=getOriginalPath(e[t-2]);return t>1&&getOriginalPath(r)===s&&a[a.length-1].path!==s?a.findIndex(isSameRouteRecord.bind(null,e[t-2])):i})),a=computed((()=>r.value>-1&&includesParams(n.params,o.value.params))),i=computed((()=>r.value>-1&&r.value===n.matched.length-1&&isSameRouteLocationParams(n.params,o.value.params)));return{route:o,href:computed((()=>o.value.href)),isActive:a,isExactActive:i,navigate:function(n={}){if(guardEvent(n)){const n=t[unref(e.replace)?"replace":"push"](unref(e.to)).catch(noop);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition((()=>n)),n}return Promise.resolve()}}}function preferSingleVNode(e){return 1===e.length?e[0]:e}const RouterLinkImpl=defineComponent({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:useLink,setup(e,{slots:t}){const n=reactive(useLink(e)),{options:o}=inject(routerKey),r=computed((()=>({[getLinkClass(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[getLinkClass(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&preferSingleVNode(t.default(n));return e.custom?o:h("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),RouterLink=RouterLinkImpl;function guardEvent(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||e.defaultPrevented||void 0!==e.button&&0!==e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function includesParams(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!isArray(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}function getOriginalPath(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const getLinkClass=(e,t,n)=>null!=e?e:null!=t?t:n,RouterViewImpl=defineComponent({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=inject(routerViewLocationKey),r=computed((()=>e.route||o.value)),a=inject(viewDepthKey,0),i=computed((()=>{let e=unref(a);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=computed((()=>r.value.matched[i.value]));provide(viewDepthKey,computed((()=>i.value+1))),provide(matchedRouteKey,s),provide(routerViewLocationKey,r);const c=ref();return watch((()=>[c.value,s.value,e.name]),(([e,t,n],[o,r,a])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&isSameRouteRecord(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,a=e.name,i=s.value,u=i&&i.components[a];if(!u)return normalizeSlot(n.default,{Component:u,route:o});const l=i.props[a],f=l?!0===l?o.params:"function"==typeof l?l(o):l:null,p=h(u,assign({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(i.instances[a]=null)},ref:c}));return normalizeSlot(n.default,{Component:p,route:o})||p}}});function normalizeSlot(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const RouterView=RouterViewImpl;function createRouter(e){const t=createRouterMatcher(e.routes,e),n=e.parseQuery||parseQuery,o=e.stringifyQuery||stringifyQuery,r=e.history,a=useCallbacks(),i=useCallbacks(),s=useCallbacks(),c=shallowRef(START_LOCATION_NORMALIZED);let u=START_LOCATION_NORMALIZED;isBrowser&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=applyToParams.bind(null,(e=>""+e)),f=applyToParams.bind(null,encodeParam),p=applyToParams.bind(null,decode);function d(e,a){if(a=assign({},a||c.value),"string"==typeof e){const o=parseURL(n,e,a.path),i=t.resolve({path:o.path},a),s=r.createHref(o.fullPath);return assign(o,i,{params:p(i.params),hash:decode(o.hash),redirectedFrom:void 0,href:s})}let i;if(null!=e.path)i=assign({},e,{path:parseURL(n,e.path,a.path).path});else{const t=assign({},e.params);for(const e in t)null==t[e]&&delete t[e];i=assign({},e,{params:f(t)}),a.params=f(a.params)}const s=t.resolve(i,a),u=e.hash||"";s.params=l(p(s.params));const d=stringifyURL(o,assign({},e,{hash:encodeHash(u),path:s.path})),h=r.createHref(d);return assign({fullPath:d,hash:u,query:o===stringifyQuery?normalizeQuery(e.query):e.query||{}},s,{redirectedFrom:void 0,href:h})}function h(e){return"string"==typeof e?parseURL(n,e,c.value.path):assign({},e)}function m(e,t){if(u!==e)return createRouterError(8,{from:t,to:e})}function g(e){return v(e)}function R(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),assign({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function v(e,t){const n=u=d(e),r=c.value,a=e.state,i=e.force,s=!0===e.replace,l=R(n);if(l)return v(assign(h(l),{state:"object"==typeof l?assign({},a,l.state):a,force:i,replace:s}),t||n);const f=n;let p;return f.redirectedFrom=t,!i&&isSameRouteLocation(o,r,n)&&(p=createRouterError(16,{to:f,from:r}),N(r,r,!0,!1)),(p?Promise.resolve(p):A(f,r)).catch((e=>isNavigationFailure(e)?isNavigationFailure(e,2)?e:w(e):T(e,f,r))).then((e=>{if(e){if(isNavigationFailure(e,2))return v(assign({replace:s},h(e.to),{state:"object"==typeof e.to?assign({},a,e.to.state):a,force:i}),t||f)}else e=P(f,r,!0,s,a);return b(f,r,e),e}))}function y(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function E(e){const t=I.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function A(e,t){let n;const[o,r,s]=extractChangingRecords(e,t);n=extractComponentsGuards(o.reverse(),"beforeRouteLeave",e,t);for(const r of o)r.leaveGuards.forEach((o=>{n.push(guardToPromiseFn(o,e,t))}));const c=y.bind(null,e,t);return n.push(c),B(n).then((()=>{n=[];for(const o of a.list())n.push(guardToPromiseFn(o,e,t));return n.push(c),B(n)})).then((()=>{n=extractComponentsGuards(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(guardToPromiseFn(o,e,t))}));return n.push(c),B(n)})).then((()=>{n=[];for(const o of s)if(o.beforeEnter)if(isArray(o.beforeEnter))for(const r of o.beforeEnter)n.push(guardToPromiseFn(r,e,t));else n.push(guardToPromiseFn(o.beforeEnter,e,t));return n.push(c),B(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=extractComponentsGuards(s,"beforeRouteEnter",e,t,E),n.push(c),B(n)))).then((()=>{n=[];for(const o of i.list())n.push(guardToPromiseFn(o,e,t));return n.push(c),B(n)})).catch((e=>isNavigationFailure(e,8)?e:Promise.reject(e)))}function b(e,t,n){s.list().forEach((o=>E((()=>o(e,t,n)))))}function P(e,t,n,o,a){const i=m(e,t);if(i)return i;const s=t===START_LOCATION_NORMALIZED,u=isBrowser?history.state:{};n&&(o||s?r.replace(e.fullPath,assign({scroll:s&&u&&u.scroll},a)):r.push(e.fullPath,a)),c.value=e,N(e,t,n,s),w()}let S;let _,L=useCallbacks(),C=useCallbacks();function T(e,t,n){w(e);const o=C.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function w(e){return _||(_=!e,S||(S=r.listen(((e,t,n)=>{if(!M.listening)return;const o=d(e),a=R(o);if(a)return void v(assign(a,{replace:!0,force:!0}),o).catch(noop);u=o;const i=c.value;isBrowser&&saveScrollPosition(getScrollKey(i.fullPath,n.delta),computeScrollPosition()),A(o,i).catch((e=>isNavigationFailure(e,12)?e:isNavigationFailure(e,2)?(v(assign(h(e.to),{force:!0}),o).then((e=>{isNavigationFailure(e,20)&&!n.delta&&n.type===NavigationType.pop&&r.go(-1,!1)})).catch(noop),Promise.reject()):(n.delta&&r.go(-n.delta,!1),T(e,o,i)))).then((e=>{(e=e||P(o,i,!1))&&(n.delta&&!isNavigationFailure(e,8)?r.go(-n.delta,!1):n.type===NavigationType.pop&&isNavigationFailure(e,20)&&r.go(-1,!1)),b(o,i,e)})).catch(noop)}))),L.list().forEach((([t,n])=>e?n(e):t())),L.reset()),e}function N(t,n,o,r){const{scrollBehavior:a}=e;if(!isBrowser||!a)return Promise.resolve();const i=!o&&getSavedScrollPosition(getScrollKey(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return nextTick().then((()=>a(t,n,i))).then((e=>e&&scrollToPosition(e))).catch((e=>T(e,t,n)))}const O=e=>r.go(e);let k;const I=new Set,M={currentRoute:c,listening:!0,addRoute:function(e,n){let o,r;return isRouteName(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:d,options:e,push:g,replace:function(e){return g(assign(h(e),{replace:!0}))},go:O,back:()=>O(-1),forward:()=>O(1),beforeEach:a.add,beforeResolve:i.add,afterEach:s.add,onError:C.add,isReady:function(){return _&&c.value!==START_LOCATION_NORMALIZED?Promise.resolve():new Promise(((e,t)=>{L.add([e,t])}))},install(e){e.component("RouterLink",RouterLink),e.component("RouterView",RouterView),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>unref(c)}),isBrowser&&!k&&c.value===START_LOCATION_NORMALIZED&&(k=!0,g(r.location).catch((e=>{})));const t={};for(const e in START_LOCATION_NORMALIZED)Object.defineProperty(t,e,{get:()=>c.value[e],enumerable:!0});e.provide(routerKey,this),e.provide(routeLocationKey,shallowReactive(t)),e.provide(routerViewLocationKey,c);const n=e.unmount;I.add(e),e.unmount=function(){I.delete(e),I.size<1&&(u=START_LOCATION_NORMALIZED,S&&S(),S=null,c.value=START_LOCATION_NORMALIZED,k=!1,_=!1),n()}}};function B(e){return e.reduce(((e,t)=>e.then((()=>E(t)))),Promise.resolve())}return M}function extractChangingRecords(e,t){const n=[],o=[],r=[],a=Math.max(t.matched.length,e.matched.length);for(let i=0;i<a;i++){const a=t.matched[i];a&&(e.matched.find((e=>isSameRouteRecord(e,a)))?o.push(a):n.push(a));const s=e.matched[i];s&&(t.matched.find((e=>isSameRouteRecord(e,s)))||r.push(s))}return[n,o,r]}function useRouter(){return inject(routerKey)}function useRoute(e){return inject(routeLocationKey)}export{NavigationFailureType,RouterLink,RouterView,START_LOCATION_NORMALIZED as START_LOCATION,createMemoryHistory,createRouter,createRouterMatcher,createWebHashHistory,createWebHistory,isNavigationFailure,loadRouteLocation,matchedRouteKey,onBeforeRouteLeave,onBeforeRouteUpdate,parseQuery,routeLocationKey,routerKey,routerViewLocationKey,stringifyQuery,useLink,useRoute,useRouter,viewDepthKey};

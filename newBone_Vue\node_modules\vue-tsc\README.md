# vue-tsc

Install: `npm i vue-tsc -D`

Usage: `vue-tsc --noEmit && vite build`

Vue 3 command line Type-Checking tool base on IDE plugin [Volar](https://github.com/vuejs/language-tools).

Roadmap:

- [x] Type-Checking with `--noEmit`
- [x] Use released LSP module
- [x] Make `typescript` as peerDependencies
- [x] Cleaner dependencies (remove `prettyhtml`, `prettier` etc.) (with `vscode-vue-languageservice` version >= 0.26.4)
- [x] dts emit support
- [x] Watch mode support

## Usage

Type check:

`vue-tsc --noEmit`

Build dts:

`vue-tsc --declaration --emitDeclarationOnly`

Check out https://github.com/vuejs/language-tools/discussions/640#discussioncomment-1555479 for example repo.

## Sponsors

<p align="center">
  <a href="https://cdn.jsdelivr.net/gh/johnsoncodehk/sponsors/sponsors.svg">
    <img src="https://cdn.jsdelivr.net/gh/johnsoncodehk/sponsors/sponsors.svg"/>
  </a>
</p>

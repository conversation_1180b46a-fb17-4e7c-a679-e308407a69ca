<script setup lang="ts">
import { RouterView } from 'vue-router'
import GlobalNotifications from '@/components/GlobalNotifications.vue'
</script>

<template>
  <div class="app-container">
    <RouterView />
    <!-- 全局通知弹窗 -->
    <GlobalNotifications />
    </div>
</template>

<style>
body, html {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  background: #000;
}

/* 方案1：完全全屏显示模式 */
.app-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  background: #000;
}

/* 方案2：1920x1080居中显示模式（注释掉方案1，启用方案2） */
/*
body, html {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1920px;
  min-height: 1080px;
}

.app-container {
  width: 1920px;
  height: 1080px;
  position: relative;
  overflow: hidden;
  background: #000;
  box-shadow: 0 0 50px rgba(255, 255, 255, 0.1);
}

/* 适应较小屏幕时的缩放 */
@media screen and (max-width: 1920px), screen and (max-height: 1080px) {
  .app-container {
    transform-origin: center;
    transform: scale(calc(min(100vw / 1920, 100vh / 1080)));
  }
}
*/

/* 全局过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
  }

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 全局禁用不必要元素的文本选择和插入光标 */
img, 
svg, 
video, 
canvas,
.t-image,
.t-icon,
.t-button,
div:not([contenteditable]),
span:not([contenteditable]),
p:not([contenteditable]) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}

/* 专门针对图片元素 */
img {
  pointer-events: auto;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 对于可点击的元素，使用指针光标 */
button,
.t-button,
[role="button"],
.clickable {
  cursor: pointer !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 输入框和可编辑元素保持文本光标 */
input,
textarea,
[contenteditable="true"],
.t-input input,
.t-textarea textarea {
  cursor: text !important;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 禁用拖拽 */
img,
svg {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}
</style>

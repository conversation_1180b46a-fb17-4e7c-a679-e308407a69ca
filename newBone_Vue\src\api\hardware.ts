import http from '@/utils/axios'

/**
 * 硬件管理相关API
 */

// 获取治疗头管理页面数据
export const getTreatmentHeads = (params: {
  page?: number
  size?: number
}) => {
  return http.get('/hardware/heads', { params })
}

/**
 * 设置治疗头指示灯（TWSC指令）
 */
export const setTreatmentHeadLights = (lightRequests: Array<{
  headNumber: number
  colorCode: number
}>) => {
  return http.post('/hardware/treatment-heads/lights', lightRequests)
}

/**
 * 关闭治疗头指示灯（TWSN指令）
 */
export const turnOffTreatmentHeadLights = (headNumbers: number[]) => {
  return http.delete('/hardware/treatment-heads/lights', { data: headNumbers })
}

/**
 * 批量设置单色指示灯
 */
export const setBatchLights = (headNumbers: number[], colorCode: number) => {
  return http.post('/hardware/treatment-heads/lights/batch', null, {
    params: { headNumbers, colorCode }
  })
}

/**
 * 关闭所有治疗头指示灯
 */
export const turnOffAllLights = () => {
  return http.post('/hardware/treatment-heads/lights/turn-off-all')
}
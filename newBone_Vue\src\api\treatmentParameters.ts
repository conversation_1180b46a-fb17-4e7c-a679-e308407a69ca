import http from '@/utils/axios'

/**
 * 治疗参数设置相关API
 */

/**
 * 检查治疗头数量是否充足
 */
export const checkTreatmentHeadAvailability = (params: {
  patientId: string
  treatmentMode: 'local' | 'takeaway'
  bodyParts: Array<{
    name: string
    color: string
    parameters: {
      time: string
      intensity: string
      frequency: string
      depth: string
      count: number
    }
  }>
}) => {
  return http.post('/treatment-parameters/check-availability', params)
}

/**
 * 生成推荐治疗头配置
 */
export const generateRecommendations = (params: {
  patientId: string
  treatmentMode: 'local' | 'takeaway'
  bodyParts: Array<{
    name: string
    color: string
    parameters: {
      time: string
      intensity: string
      frequency: string
      depth: string
      count: number
    }
  }>
}) => {
  return http.post('/treatment-parameters/generate-recommendations', params)
}

/**
 * 下载治疗参数（取走治疗模式）
 */
export const downloadTreatmentParameters = (params: {
  patientId: string
  treatmentMode: 'takeaway'
  bodyParts: Array<{
    name: string
    color: string
    parameters: {
      time: string
      intensity: string
      frequency: string
      depth: string
      count: number
    }
  }>
}) => {
  return http.post('/treatment-parameters/download', params)
}

/**
 * 启动本地治疗
 */
export const startLocalTreatment = (params: {
  patientId: string
  treatmentMode: 'local'
  bodyParts: Array<{
    name: string
    color: string
    parameters: {
      time: string
      intensity: string
      frequency: string
      depth: string
      count: number
    }
  }>
}) => {
  return http.post('/treatment-parameters/start-local', params)
}

/**
 * 确认治疗头选择并发送参数
 */
export const confirmTreatmentHeadSelection = (params: {
  patientId: string
  treatmentMode: 'local' | 'takeaway'
  recommendedHeads: Array<{
    headNumber: number
    slotNumber: number
    targetBodyPart: string
    lightColor: number
    lightColorName: string
    durationMinutes: number
    intensity: number
    frequency: number
    recommendationReason: string
    priority: number
    compartmentType: string
  }>
  treatmentParams: {
    durationMinutes: number
    intensity: number
    frequency: number
    headNumbers: number[]
  }
}) => {
  return http.post('/treatment-parameters/confirm-selection', params)
} 
<template>
  <div class="battery-container">
    <div class="battery-icon" :class="batteryClass">
      <div 
        class="battery-fill"
        :style="batteryStyle"
      ></div>
    </div>
    <div class="battery-tip"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  level: number; // 电量百分比 0-100
  size?: 'small' | 'medium' | 'large';
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium'
});

// 计算电量颜色
const batteryColor = computed(() => {
  if (props.level >= 70) return '#00ff00'; // 绿色
  if (props.level >= 30) return '#ffff00'; // 黄色
  return '#ff0000'; // 红色
});

// 计算电量等级class
const batteryClass = computed(() => {
  return [
    `battery-${props.size}`,
    {
      'battery-critical': props.level < 20,
      'battery-low': props.level >= 20 && props.level < 50,
      'battery-medium': props.level >= 50 && props.level < 80,
      'battery-high': props.level >= 80
    }
  ];
});

// 计算电量填充样式
const batteryStyle = computed(() => {
  return {
    width: `${Math.max(0, Math.min(100, props.level))}%`,
    backgroundColor: batteryColor.value
  };
});
</script>

<style scoped>
.battery-container {
  display: flex;
  align-items: center;
  gap: 2px;
}

.battery-icon {
  border: 2px solid #333;
  border-radius: 3px;
  position: relative;
  background: #f0f0f0;
  overflow: hidden;
}

.battery-small {
  width: 24px;
  height: 12px;
}

.battery-medium {
  width: 36px;
  height: 20px;
}

.battery-large {
  width: 48px;
  height: 28px;
}

.battery-tip {
  width: 3px;
  height: 8px;
  background: #333;
  border-radius: 0 2px 2px 0;
}

.battery-medium + .battery-tip {
  height: 8px;
}

.battery-large + .battery-tip {
  height: 12px;
}

.battery-fill {
  height: 100%;
  border-radius: 1px;
  transition: all 0.3s ease;
  position: relative;
}

.battery-critical {
  border-color: #ff0000;
}

.battery-critical .battery-tip {
  background: #ff0000;
}

.battery-low {
  border-color: #ff6600;
}

.battery-medium {
  border-color: #ffff00;
}

.battery-high {
  border-color: #00ff00;
}

/* 动画效果 */
.battery-critical .battery-fill {
  animation: battery-blink 1s infinite;
}

@keyframes battery-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* hover效果 */
.battery-container:hover .battery-icon {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}
</style> 
<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <!-- 只复刻 section_1 部分 -->
      <div class="section_1 flex-col">
        <div class="text-wrapper_1 flex-row">
          <span class="text_1">下仓</span>
        </div>
        <div class="text-wrapper_text_2 flex-row">
          <span class="text_2">上仓</span>
        </div>
        <div class="group_2 flex-row">
          <div class="box_2 flex-col">
            <!-- 上层治疗仓 (槽位1-10) -->
            <div class="box_3 flex-col">
              <!-- 槽位行1: 槽位1-2 -->
              <div class="image-wrapper_1 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景1" />
                  <img 
                    v-if="getTreatmentHeadImage(1, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(1, 1)"
                    :title="getTreatmentHeadTooltip(1, 1)"
                    alt="治疗头1"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景2" />
                  <img 
                    v-if="getTreatmentHeadImage(1, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(1, 2)"
                    :title="getTreatmentHeadTooltip(1, 2)"
                    alt="治疗头2"
                  />
                </div>
              </div>
              <!-- 槽位行2-5 (槽位3-10) -->
              <div class="image-wrapper_2 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景3" />
                  <img 
                    v-if="getTreatmentHeadImage(2, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(2, 1)"
                    :title="getTreatmentHeadTooltip(2, 1)"
                    alt="治疗头3"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景4" />
                  <img 
                    v-if="getTreatmentHeadImage(2, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(2, 2)"
                    :title="getTreatmentHeadTooltip(2, 2)"
                    alt="治疗头4"
                  />
                </div>
              </div>
              <div class="image-wrapper_3 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景5" />
                  <img 
                    v-if="getTreatmentHeadImage(3, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(3, 1)"
                    :title="getTreatmentHeadTooltip(3, 1)"
                    alt="治疗头5"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景6" />
                  <img 
                    v-if="getTreatmentHeadImage(3, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(3, 2)"
                    :title="getTreatmentHeadTooltip(3, 2)"
                    alt="治疗头6"
                  />
                </div>
              </div>
              <div class="image-wrapper_4 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景7" />
                  <img 
                    v-if="getTreatmentHeadImage(4, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(4, 1)"
                    :title="getTreatmentHeadTooltip(4, 1)"
                    alt="治疗头7"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景8" />
                  <img 
                    v-if="getTreatmentHeadImage(4, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(4, 2)"
                    :title="getTreatmentHeadTooltip(4, 2)"
                    alt="治疗头8"
                  />
                </div>
              </div>
              <div class="image-wrapper_5 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景9" />
                  <img 
                    v-if="getTreatmentHeadImage(5, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(5, 1)"
                    :title="getTreatmentHeadTooltip(5, 1)"
                    alt="治疗头9"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景10" />
                  <img 
                    v-if="getTreatmentHeadImage(5, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(5, 2)"
                    :title="getTreatmentHeadTooltip(5, 2)"
                    alt="治疗头10"
                  />
                </div>
              </div>
              <!-- 上层装饰元素 -->
              <div class="image-wrapper_6 flex-col justify-center">
                <!-- <img class="image_11" :src="upperBackground" alt="上仓治疗头" /> -->
              </div>
              <!-- <img class="image_12" :src="upperShelf" alt="上仓治疗头" /> -->
              <div class="image-wrapper_7 flex-col">
                <!-- 移除原来固定的治疗头图片 -->
                <!-- <img class="image_13" :src="upperLeft" alt="上仓左侧" /> -->
                <!-- <img class="image_14" :src="upperRight" alt="上仓右侧" /> -->
              </div>
            </div>
            
            <!-- 下层治疗仓 (槽位11-20) -->
            <div class="box_4 flex-col">
              <!-- 继续使用原有的下层槽位 -->
              <div class="image-wrapper_8 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景11" />
                  <img 
                    v-if="getTreatmentHeadImage(6, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(6, 1)"
                    :title="getTreatmentHeadTooltip(6, 1)"
                    alt="治疗头11"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景12" />
                  <img 
                    v-if="getTreatmentHeadImage(6, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(6, 2)"
                    :title="getTreatmentHeadTooltip(6, 2)"
                    alt="治疗头12"
                  />
                </div>
              </div>
              <!-- 其他槽位省略，结构相同 -->
              <div class="image-wrapper_9 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景13" />
                  <img 
                    v-if="getTreatmentHeadImage(7, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(7, 1)"
                    :title="getTreatmentHeadTooltip(7, 1)"
                    alt="治疗头13"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景14" />
                  <img 
                    v-if="getTreatmentHeadImage(7, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(7, 2)"
                    :title="getTreatmentHeadTooltip(7, 2)"
                    alt="治疗头14"
                  />
                </div>
              </div>
              <div class="image-wrapper_10 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景15" />
                  <img 
                    v-if="getTreatmentHeadImage(8, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(8, 1)"
                    :title="getTreatmentHeadTooltip(8, 1)"
                    alt="治疗头15"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景16" />
                  <img 
                    v-if="getTreatmentHeadImage(8, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(8, 2)"
                    :title="getTreatmentHeadTooltip(8, 2)"
                    alt="治疗头16"
                  />
                </div>
              </div>
              <div class="image-wrapper_11 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景17" />
                  <img 
                    v-if="getTreatmentHeadImage(9, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(9, 1)"
                    :title="getTreatmentHeadTooltip(9, 1)"
                    alt="治疗头17"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景18" />
                  <img 
                    v-if="getTreatmentHeadImage(9, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(9, 2)"
                    :title="getTreatmentHeadTooltip(9, 2)"
                    alt="治疗头18"
                  />
                </div>
              </div>
              <div class="image-wrapper_12 flex-row justify-between">
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景19" />
                  <img 
                    v-if="getTreatmentHeadImage(10, 1)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(10, 1)"
                    :title="getTreatmentHeadTooltip(10, 1)"
                    alt="治疗头19"
                  />
                </div>
                <div class="slot-container">
                  <img class="slot-bg" :src="slotBackground" alt="槽位背景20" />
                  <img 
                    v-if="getTreatmentHeadImage(10, 2)"
                    class="treatment-head-overlay"
                    :src="getTreatmentHeadImage(10, 2)"
                    :title="getTreatmentHeadTooltip(10, 2)"
                    alt="治疗头20"
                  />
                </div>
              </div>
              <!-- 下层装饰元素 -->
              <div class="image-wrapper_13 flex-col justify-center">
                <!-- <img class="image_25" :src="lowerBackground" alt="下仓背景" /> -->
              </div>
              <!-- <img class="image_26" :src="lowerShelf" alt="下仓架子" /> -->
              <div class="image-wrapper_14 flex-col">
                <!-- 移除原来固定的治疗头图片 -->
                <!-- <img class="image_27" :src="lowerLeft" alt="下仓左侧" /> -->
                <!-- <img class="image_28" :src="lowerRight" alt="下仓右侧" /> -->
              </div>
            </div>
          </div>
          <div class="box_5 flex-col justify-between">
            <div class="group_3 flex-col" ref="group3Ref">
              <!-- 使用img元素显示温馨提示背景 -->
              <img 
                v-if="props.treatmentMode === 'takeaway'"
                :src="tipMode1" 
                alt="取走治疗温馨提示"
                class="tip-background-image"
              />
              <img 
                v-else
                :src="tipMode2" 
                alt="本地治疗温馨提示"
                class="tip-background-image"
              />
              <div class="group_4 flex-row justify-between">
                <!-- 温馨提示内容现在包含在背景图中 -->
              </div>
            </div>
            <div class="group_5 flex-col">
              <div class="text-wrapper_2 flex-col" @click="confirmSelection">
                <span class="text_4">确认</span>
              </div>
            </div>
          </div>
          <img
            class="image_30"
            :src="closeButton"
            alt="关闭按钮"
            @click="closeModal"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, onMounted } from 'vue'

// 重命名图片导入，提高可读性
import slotBackground from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/2396b697e6cf7ef24362823e1a712cba.png' // 槽位背景
import upperBackground from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/596f24f0d0783cebd1a11520c4a545bc.png'
import upperShelf from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/5051961f906e80bb82cfe3b05207bb12.png'
import upperLeft from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/5e48a55c46d1dd5b6c06d85dd9fb5d54.png'
import upperRight from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/48237acfa9cf62d643761d5eee63e9ac.png'
import lowerBackground from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/403a914b542bf1d89c2cddb08465ef54.png'
import lowerShelf from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/d87077b7b28f4cfcbaee1dff4b7b68b9.png'
import lowerLeft from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/a30eaf077672b77e38c54d1233124e42.png'
import lowerRight from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/c4fada63945254b3b7a60dd8d49935c1.png'
import closeButton from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/791537672adcca65f014ca0a09612aa5.png'

// 治疗头图片
import greenTreatmentHead from '@/assets/images/treatmentsettingsview/img/绿色灯治疗头.png'
import orangeTreatmentHead from '@/assets/images/treatmentsettingsview/img/橙色灯治疗头.png'
import blueTreatmentHead from '@/assets/images/treatmentsettingsview/img/蓝色灯治疗头.png'
import notInvolvedTreatmentHead from '@/assets/images/treatmentsettingsview/img/未涉及治疗头.png'

// 温馨提示背景图片 - 使用正确的路径
import tipMode1 from '@/assets/images/交互界面/参数设置/温馨提示本地.png'
import tipMode2 from '@/assets/images/交互界面/参数设置/温馨提示远端.png'

// Props
const props = defineProps<{
  visible: boolean
  recommendedHeads?: any[]  // 推荐的治疗头数据
  treatmentMode?: 'local' | 'takeaway' // 治疗模式：本地治疗 | 取走治疗
}>()

// Emits
const emit = defineEmits<{
  close: []
  confirm: []
}>()

// 添加ref引用用于调试
const group3Ref = ref<HTMLElement | null>(null)

// 调试背景图片加载
onMounted(() => {
  console.log('TreatmentHeadSelectionModal mounted')
  console.log('tipMode1 path:', tipMode1)
  console.log('tipMode2 path:', tipMode2)
  console.log('Treatment mode:', props.treatmentMode)
  
  // 尝试直接设置背景图片
  if (group3Ref.value) {
    const element = group3Ref.value
    const backgroundImage = props.treatmentMode === 'takeaway' ? tipMode1 : tipMode2
    console.log('Setting background image:', backgroundImage)
    element.style.backgroundImage = `url("${backgroundImage}")`
    element.style.backgroundRepeat = 'no-repeat'
    element.style.backgroundSize = '441px 540px'
    element.style.backgroundPosition = '0px 0px'
  }
})

// 模拟治疗头数据（可以从父组件传入）
const mockTreatmentHeads = computed(() => {
  return props.recommendedHeads || [
    // 模拟一些推荐的治疗头数据
    { row: 1, position: 1, color: 'green', partName: '腰背部', depth: '深部' },
    { row: 2, position: 2, color: 'blue', partName: '肩颈部', depth: '浅部' },
    { row: 7, position: 1, color: 'orange', partName: '髋部', depth: '深部' },
    // 模拟一些未涉及的治疗头
    { row: 3, position: 1, color: 'not-involved', partName: '', depth: '' },
    { row: 8, position: 2, color: 'not-involved', partName: '', depth: '' },
  ]
})

// 获取治疗头图片 - 修复返回类型
const getTreatmentHeadImage = (row: number, position: number): string | undefined => {
  const treatmentHead = mockTreatmentHeads.value.find(
    head => head.row === row && head.position === position
  )
  
  if (!treatmentHead) {
    return undefined // 空槽位不显示治疗头
  }
  
  switch (treatmentHead.color) {
    case 'green':
      return greenTreatmentHead
    case 'orange':
      return orangeTreatmentHead
    case 'blue':
      return blueTreatmentHead
    case 'not-involved':
      return notInvolvedTreatmentHead
    default:
      return undefined
  }
}

// 获取治疗头提示信息
const getTreatmentHeadTooltip = (row: number, position: number) => {
  const treatmentHead = mockTreatmentHeads.value.find(
    head => head.row === row && head.position === position
  )
  
  if (!treatmentHead) {
    return '空槽位'
  }
  
  if (treatmentHead.color === 'not-involved') {
    return '现有治疗头（未涉及本次治疗）'
  }
  
  return `推荐：${treatmentHead.partName} | ${treatmentHead.depth} | ${treatmentHead.color}色指示灯`
}

// 计算温馨提示背景图片 - 使用computed确保响应性
const backgroundImageStyle = computed(() => {
  const backgroundImage = props.treatmentMode === 'takeaway' ? tipMode1 : tipMode2
  console.log('治疗模式:', props.treatmentMode, '背景图片路径:', backgroundImage)
  
  return {
    backgroundImage: `url("${backgroundImage}")`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: '441px 540px', // 使用原始尺寸
    backgroundPosition: '0px 0px'
  }
})

// 计算温馨提示背景图片
const getTipBackgroundImage = () => {
  const backgroundImage = props.treatmentMode === 'takeaway' ? tipMode1 : tipMode2
  console.log('治疗模式:', props.treatmentMode, '背景图片路径:', backgroundImage)
  return backgroundImage
}

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const closeModal = () => {
  emit('close')
}

const confirmSelection = () => {
  emit('confirm')
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: transparent;
  overflow: hidden;
  position: absolute;
  top: 65px;
  left: 334px;
}

/* 复刻原始样式 - 只包含 section_1 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

/* section_1 - 主弹窗容器 */
.section_1 {
  box-shadow: 4px 7px 52px 1px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 16px;
  height: 949px;
  border: 3px solid rgba(92, 212, 200, 1);
  width: 1208px;
  position: relative;
}

.text-wrapper_1 {
  width: 87px;
  height: 33px;
  margin: 755px 0 0 304px;
}

.text_1 {
  width: 87px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 165px;
}

.group_2 {
  position: absolute;
  left: 144px;
  top: 31px;
  width: 1019px;
  height: 811px;
}

.box_2 {
  width: 408px;
  height: 789px;
  margin-top: 42px;
}

.text-wrapper_text_2 {
  width: 87px;
  height: 33px;
  margin: -840px 0 0 159px;
}

.text_2 {
  width: 87px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 215px;
  margin-left: 145px;
}

.box_3 {
  height: 367px;
  background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/5152c094067a79cd409c54eef84cf3bc.png') 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 5px;
  width: 408px;
  position: relative;
}

/* 槽位容器样式 - 核心改进 */
.slot-container {
  position: relative;
  display: inline-block;
  width: 122px;
  height: 18px; /* 保持原始高度，不影响槽位布局 */
  overflow: visible; /* 允许治疗头超出容器显示 */
}

.slot-bg {
  width: 122px;
  height: 18px;
  cursor: pointer;
  position: relative; /* 改为相对定位，保持原始布局 */
}

.treatment-head-overlay {
  position: absolute;
  bottom: 0; /* 底部对齐槽位底部 */
  left: 0;
  width: 122px;
  height: 50px; /* 和槽位一样的高度，完全覆盖 */
  object-fit: contain;
  z-index: 2;
  pointer-events: none;
}

/* 槽位行样式 */
.image-wrapper_1, .image-wrapper_2, .image-wrapper_4, .image-wrapper_5,
.image-wrapper_8, .image-wrapper_9, .image-wrapper_11, .image-wrapper_12 {
  width: 273px;
  height: 18px; /* 恢复原始高度，保持槽位位置精确 */
  margin: 25px 0 0 55px;
}

.image-wrapper_3, .image-wrapper_10 {
  width: 273px;
  height: 21px; /* 恢复原始高度，保持槽位位置精确 */
  margin: 32px 0 0 55px;
}

.image-wrapper_1 {
  margin: 62px 0 0 55px;
}

.image-wrapper_5 {
  margin: 32px 0 98px 55px;
}

.image-wrapper_8 {
  margin: 62px 0 0 55px;
}

.image-wrapper_12 {
  margin: 32px 0 98px 55px;
}

.image-wrapper_6 {
  height: 50px;
  /*background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/d9d24d14a9e926fb56de667c05530e1b.png') 100% no-repeat;*/
  background-size: 100% 100%;
  width: 122px;
  position: absolute;
  left: 207px;
  top: 29px;
}

.image_11 {
  width: 122px;
  height: 50px;
}

.image_12 {
  position: absolute;
  left: 207px;
  top: 123px;
  width: 122px;
  height: 50px;
}

.image-wrapper_7 {
  position: absolute;
  left: 56px;
  top: 30px;
  width: 122px;
  height: 50px;
  /*background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/44e10394a537fdee3a5270878f1c7ba2.png') 100% no-repeat;*/
  background-size: 100% 100%;
}

.image_13 {
  position: absolute;
  left: 0;
  top: 92px;
  width: 122px;
  height: 50px;
}

.image_14 {
  position: absolute;
  left: 1px;
  top: 43px;
  width: 122px;
  height: 50px;
}

.box_4 {
  height: 367px;
  background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/81af175f3bb76cdbc04c0e0a963c84af.png') 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 18px;
  width: 408px;
  position: relative;
}

.image-wrapper_13 {
  height: 50px;
  /*background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/1b43bd65afe98bab350d75e7bba53a41.png') 100% no-repeat;*/
  background-size: 100% 100%;
  width: 122px;
  position: absolute;
  left: 207px;
  top: 29px;
}

.image_25 {
  width: 122px;
  height: 50px;
}

.image_26 {
  position: absolute;
  left: 207px;
  top: 123px;
  width: 122px;
  height: 50px;
}

.image-wrapper_14 {
  position: absolute;
  left: 56px;
  top: 30px;
  width: 122px;
  height: 50px;
  /*background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/c49ec2ffeadf1111877902f6f3472ea7.png') 100% no-repeat;*/
  background-size: 100% 100%;
}

.image_27 {
  position: absolute;
  left: 0;
  top: 92px;
  width: 122px;
  height: 50px;
}

.image_28 {
  position: absolute;
  left: 1px;
  top: 43px;
  width: 122px;
  height: 50px;
}

.box_5 {
  width: 445px;
  height: 678px;
  margin: 69px 0 0 93px;
}

.group_3 {
  width: 449px;
  height: 542px;
  position: relative; /* 确保内部绝对定位的图片相对于这个容器定位 */
  /* 移除可能冲突的background相关样式，让动态样式生效 */
}

.group_4 {
  width: 298px;
  height: 63px;
  margin: 90px 0 0 70px;
  position: relative; /* 确保在图片上方 */
  z-index: 1;
}

.group_5 {
  height: 69px;
  background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/5285ef37f0c7ca55a77fffdac391cdf9.png') -10px 0px no-repeat;
  background-size: 253px 92px;
  width: 232px;
  margin: 67px 0 0 110px;
  position: relative; /* 确保在图片上方 */
  z-index: 1;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text_4 {
  width: 93px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
}

.image_30 {
  width: 100px;
  height: 100px;
  margin-left: 17px;
  cursor: pointer;
  position: absolute;
  right: -20px;
  top: -20px;
}

/* 新增样式用于提示背景图片 */
.tip-background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 441px;
  height: 540px;
  object-fit: contain; /* 保持图片比例 */
  z-index: 0; /* 确保图片在其他内容下方 */
}
</style> 
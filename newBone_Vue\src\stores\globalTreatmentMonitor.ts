import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useRouter } from 'vue-router'
import http from '@/utils/axios'

// 治疗详情状态枚举
export enum TreatmentDetailStatus {
  TREATING = 'TREATING',
  COMPLETED = 'COMPLETED',
  AWAITING_RETURN = 'AWAITING_RETURN',
  RETURNED = 'RETURNED',
  TERMINATED = 'TERMINATED'
}

// 进程状态枚举
export enum ProcessStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 治疗模式枚举
export enum TreatmentMode {
  ON_SITE = 'ON_SITE',
  TAKE_AWAY = 'TAKE_AWAY'
}

// 通知类型
export enum NotificationType {
  TREATMENT_COMPLETED = 'TREATMENT_COMPLETED',
  PICKUP_REMINDER = 'PICKUP_REMINDER'
}

// 治疗详情接口
export interface TreatmentDetail {
  detailId: number
  bodyPart: string
  status: string
  processId: number
  patientName: string
  treatmentMode: string
  headNumberUsed?: number // 使用的治疗头编号
  duration?: number // 治疗时长（分钟）
  startTime?: string // 开始时间
  remainingTimeSeconds?: number // 剩余时间（秒）
  elapsedTimeSeconds?: number // 已用时间（秒）
  totalDurationSeconds?: number // 总时长（秒）
}

// 进程信息接口
export interface ProcessInfo {
  processId: number
  patientName: string
  treatmentMode: string
  status: string
  details: TreatmentDetail[]
}

// 通知数据接口
export interface NotificationData {
  id: string
  type: NotificationType
  processId: number
  patientName: string
  message: string
  timestamp: string
  headNumbers?: number[] // 治疗头编号数组（用于待取回通知）
}

export const useGlobalTreatmentMonitorStore = defineStore('globalTreatmentMonitor', () => {
  // 状态
  const isMonitoring = ref(false)
  const activeProcesses = ref<ProcessInfo[]>([])
  const notifications = ref<NotificationData[]>([])
  const lastCheckTime = ref<Date | null>(null)
  const monitorInterval = ref<number | null>(null)
  
  // 监听间隔（毫秒）
  const MONITOR_INTERVAL = 5000 // 5秒检查一次
  
  // 需要排除的页面路由
  const EXCLUDED_ROUTES = [
    '/treatment-process',
    '/treatment-process-takeaway'
  ]

  // 计算属性
  const hasActiveNotifications = computed(() => notifications.value.length > 0)
  
  // 检查当前路由是否应该显示通知
  const shouldShowNotifications = (currentRoute: string): boolean => {
    return !EXCLUDED_ROUTES.some(route => currentRoute.startsWith(route))
  }

  // 获取所有进行中的治疗进程
  const fetchActiveProcesses = async (): Promise<ProcessInfo[]> => {
    try {
      const response = await http.get('/processes/details')
      if (response.data && response.data.details) {
        // 按进程ID分组
        const processMap = new Map<number, ProcessInfo>()
        
        response.data.details.forEach((detail: any) => {
          if (!processMap.has(detail.processId)) {
            processMap.set(detail.processId, {
              processId: detail.processId,
              patientName: detail.patientName,
              treatmentMode: detail.treatmentMode,
              status: detail.processStatus,
              details: []
            })
          }
          
          processMap.get(detail.processId)!.details.push({
            detailId: detail.detailId,
            bodyPart: detail.bodyPart,
            status: detail.status,
            processId: detail.processId,
            patientName: detail.patientName,
            treatmentMode: detail.treatmentMode,
            headNumberUsed: detail.headNumberUsed,
            duration: detail.duration,
            startTime: detail.startTime,
            remainingTimeSeconds: detail.remainingTimeSeconds,
            elapsedTimeSeconds: detail.elapsedTimeSeconds,
            totalDurationSeconds: detail.totalDurationSeconds
          })
        })
        
        return Array.from(processMap.values()).filter(process => 
          process.status === ProcessStatus.IN_PROGRESS
        )
      }
      return []
    } catch (error) {
      console.error('获取活跃进程失败:', error)
      return []
    }
  }

  // 检查时间到期并自动更新状态
  const checkTimeExpiredAndUpdate = async (processes: ProcessInfo[]): Promise<void> => {
    for (const process of processes) {
      for (const detail of process.details) {
        if (detail.status === TreatmentDetailStatus.TREATING && 
            detail.remainingTimeSeconds !== undefined && 
            detail.remainingTimeSeconds <= 0) {
          
          console.log(`治疗详情 ${detail.detailId} 时间到期，自动更新状态`)
          
          try {
            if (detail.treatmentMode === TreatmentMode.ON_SITE) {
              // 本地治疗：更新为已完成
              await http.put(`/treatment-process/detail/${detail.detailId}/complete`)
              console.log(`治疗详情 ${detail.detailId} 已自动完成`)
            } else if (detail.treatmentMode === TreatmentMode.TAKE_AWAY) {
              // 取走治疗：更新为待取回
              await http.put(`/treatment-process/detail/${detail.detailId}/awaiting-return`)
              console.log(`治疗详情 ${detail.detailId} 已自动设为待取回`)
            }
          } catch (error) {
            console.error(`自动更新治疗详情 ${detail.detailId} 状态失败:`, error)
          }
        }
      }
    }
  }

  // 检查是否需要触发通知
  const checkForNotifications = async (processes: ProcessInfo[], currentRoute: string): Promise<void> => {
    if (!shouldShowNotifications(currentRoute)) {
      console.log('当前页面不显示通知，跳过通知检查')
      return
    }

    for (const process of processes) {
      const completedDetails = process.details.filter(d => d.status === TreatmentDetailStatus.COMPLETED)
      const awaitingReturnDetails = process.details.filter(d => d.status === TreatmentDetailStatus.AWAITING_RETURN)
      
      // 检查本地治疗完成通知
      if (process.treatmentMode === TreatmentMode.ON_SITE && 
          completedDetails.length === process.details.length && 
          completedDetails.length > 0) {
        
        const existingNotification = notifications.value.find(n => 
          n.processId === process.processId && n.type === NotificationType.TREATMENT_COMPLETED
        )
        
        if (!existingNotification) {
          const notification: NotificationData = {
            id: `completed-${process.processId}-${Date.now()}`,
            type: NotificationType.TREATMENT_COMPLETED,
            processId: process.processId,
            patientName: process.patientName,
            message: `${process.patientName} 的治疗已完成`,
            timestamp: new Date().toISOString()
          }
          
          notifications.value.push(notification)
          console.log('触发治疗完成通知:', notification)
        }
      }
      
      // 检查取走治疗待取回通知
      if (process.treatmentMode === TreatmentMode.TAKE_AWAY && 
          awaitingReturnDetails.length === process.details.length && 
          awaitingReturnDetails.length > 0) {
        
        const existingNotification = notifications.value.find(n => 
          n.processId === process.processId && n.type === NotificationType.PICKUP_REMINDER
        )
        
        if (!existingNotification) {
          // 收集治疗头编号
          const headNumbers = awaitingReturnDetails
            .map(d => d.headNumberUsed)
            .filter(num => num !== undefined) as number[]
          
          const notification: NotificationData = {
            id: `pickup-${process.processId}-${Date.now()}`,
            type: NotificationType.PICKUP_REMINDER,
            processId: process.processId,
            patientName: process.patientName,
            message: headNumbers.length > 0 
              ? `${headNumbers.join(',')}号治疗头待取回`
              : `${process.patientName} 的治疗头待取回`,
            timestamp: new Date().toISOString(),
            headNumbers
          }
          
          notifications.value.push(notification)
          console.log('触发待取回通知:', notification)
        }
      }
    }
  }

  // 执行监听检查
  const performMonitorCheck = async (currentRoute: string): Promise<void> => {
    try {
      console.log('执行全局状态监听检查...')
      
      // 获取活跃进程
      const processes = await fetchActiveProcesses()
      activeProcesses.value = processes
      
      // 检查时间到期并自动更新状态
      await checkTimeExpiredAndUpdate(processes)
      
      // 重新获取进程（状态可能已更新）
      const updatedProcesses = await fetchActiveProcesses()
      activeProcesses.value = updatedProcesses
      
      // 检查通知
      await checkForNotifications(updatedProcesses, currentRoute)
      
      lastCheckTime.value = new Date()
      console.log(`监听检查完成，发现 ${updatedProcesses.length} 个活跃进程`)
      
    } catch (error) {
      console.error('全局状态监听检查失败:', error)
    }
  }

  // 开始监听
  const startMonitoring = (currentRoute: string): void => {
    if (isMonitoring.value) {
      console.log('全局状态监听已在运行')
      return
    }
    
    console.log('开始全局状态监听')
    isMonitoring.value = true
    
    // 立即执行一次检查
    performMonitorCheck(currentRoute)
    
    // 设置定时器
    monitorInterval.value = window.setInterval(() => {
      performMonitorCheck(currentRoute)
    }, MONITOR_INTERVAL)
  }

  // 停止监听
  const stopMonitoring = (): void => {
    if (!isMonitoring.value) {
      return
    }
    
    console.log('停止全局状态监听')
    isMonitoring.value = false
    
    if (monitorInterval.value) {
      clearInterval(monitorInterval.value)
      monitorInterval.value = null
    }
    
    // 清空状态
    activeProcesses.value = []
    notifications.value = []
    lastCheckTime.value = null
  }

  // 移除通知
  const removeNotification = (notificationId: string): void => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
      console.log('移除通知:', notificationId)
    }
  }

  // 清空所有通知
  const clearAllNotifications = (): void => {
    notifications.value = []
    console.log('清空所有通知')
  }

  return {
    // 状态
    isMonitoring,
    activeProcesses,
    notifications,
    lastCheckTime,
    
    // 计算属性
    hasActiveNotifications,
    
    // 方法
    shouldShowNotifications,
    startMonitoring,
    stopMonitoring,
    performMonitorCheck,
    removeNotification,
    clearAllNotifications
  }
})

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getRecords, getRecordsCandidates, deleteRecord } from '@/api'

export const useRecordsStore = defineStore('records', () => {
  // 状态
  const records = ref<any[]>([])
  const candidates = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取所有档案数据
  const fetchAllRecords = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await getRecords({
        page: 1,
        size: 1000, // 获取所有数据
        search: undefined,
        cardId: undefined
      })
      
      if (response.data && response.data.records && Array.isArray(response.data.records)) {
        records.value = response.data.records.map((record: any) => ({
          id: record.patientId,
          name: record.name,
          medicalCardId: record.cardId,
          medicalRecordId: record.cardId,
          sex: record.gender === '男' ? 0 : 1,
          age: record.age?.replace('岁', '') || '',
          contactInfo: record.phone || '',
          creationTimestamp: record.createdDate,
          treatmentSite: '其他部位',
          treatmentCount: record.totalTreatments || 0
        }))
      } else {
        records.value = []
      }
    } catch (err) {
      console.error('获取档案列表失败:', err)
      error.value = '获取档案列表失败'
      records.value = []
    } finally {
      loading.value = false
    }
  }

  // 获取候选列表
  const fetchAllCandidates = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await getRecordsCandidates({
        page: 1,
        size: 1000, // 获取所有数据
        search: undefined
      })
      
      if (response.data && response.data.candidates && Array.isArray(response.data.candidates)) {
        candidates.value = response.data.candidates.map((candidate: any) => ({
          id: candidate.id || `temp_${Date.now()}_${Math.random()}`,
          name: candidate.name,
          medicalCardId: candidate.cardId,
          medicalRecordId: candidate.cardId,
          sex: candidate.gender === '男' ? 0 : 1,
          age: candidate.age?.replace('岁', '') || '',
          contactInfo: candidate.phone || '',
          creationTimestamp: candidate.appointmentTime,
          treatmentSite: candidate.bodyPart || '其他部位',
          treatmentCount: candidate.sessions || 0
        }))
      } else {
        candidates.value = []
      }
    } catch (err) {
      console.error('获取候选列表失败:', err)
      error.value = '获取候选列表失败'
      candidates.value = []
    } finally {
      loading.value = false
    }
  }

  // 删除档案
  const removeRecord = async (recordId: string | number, password: string) => {
    try {
      await deleteRecord(recordId, password)
      // 从本地状态中移除
      records.value = records.value.filter(record => record.medicalRecordId !== recordId)
      return { success: true }
    } catch (err) {
      console.error('删除档案失败:', err)
      return { success: false, error: err }
    }
  }

  // 计算属性 - 根据搜索条件过滤档案
  const filteredRecords = computed(() => {
    return (searchName: string = '', searchCardId: string = '') => {
      let result = [...records.value]
      
      if (searchName && searchName.trim()) {
        result = result.filter(record =>
          record.name && record.name.toLowerCase().includes(searchName.toLowerCase().trim())
        )
      }
      
      if (searchCardId && searchCardId.trim()) {
        result = result.filter(record =>
          record.medicalRecordId && record.medicalRecordId.toString().includes(searchCardId.trim())
        )
      }
      
      return result
    }
  })

  // 计算属性 - 根据搜索条件过滤候选
  const filteredCandidates = computed(() => {
    return (searchKeyword: string = '') => {
      let result = [...candidates.value]
      
      if (searchKeyword && searchKeyword.trim()) {
        const keyword = searchKeyword.toLowerCase()
        result = result.filter(candidate => 
          (candidate.name && candidate.name.toLowerCase().includes(keyword)) ||
          (candidate.medicalRecordId && candidate.medicalRecordId.toLowerCase().includes(keyword)) ||
          (candidate.medicalCardId && candidate.medicalCardId.toLowerCase().includes(keyword))
        )
      }
      
      return result
    }
  })

  return {
    // 状态
    records,
    candidates,
    loading,
    error,
    
    // 操作
    fetchAllRecords,
    fetchAllCandidates,
    removeRecord,
    
    // 计算属性
    filteredRecords,
    filteredCandidates
  }
}) 
import axios from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';
import { hasValidToken, autoLogin, clearAuth } from './auth';

// 自定义错误类型
interface CustomError extends Error {
  response?: {
    status: number;
    data: { message: string };
  };
}

// 创建axios实例
const http = axios.create({
  baseURL: '/api', // 只设置一次/api前缀
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
http.interceptors.request.use(
  async config => {
    // 如果是登录请求，跳过自动登录逻辑
    if (config.url?.includes('/auth/login')) {
      return config;
    }

    // 检查是否有Token，没有则尝试自动登录
    if (!hasValidToken()) {
      console.log('没有有效Token，尝试自动登录...');
      await autoLogin();
    }

    // 添加JWT Token认证
    const token = localStorage.getItem('jwtToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  response => {
    // 适配新的ApiResponse格式
    const data = response.data;
    
    // 如果是新的ApiResponse格式
    if (data && typeof data === 'object' && 'code' in data && 'message' in data) {
      if (data.code === 200) {
        // 成功响应，返回data字段
        response.data = data.data;
        return response;
      } else {
        // 业务错误，抛出异常
        const error = new Error(data.message || '请求失败') as CustomError;
        error.response = { 
          status: data.code, 
          data: { message: data.message } 
        };
        return Promise.reject(error);
      }
    }
    
    // 其他格式保持不变
    return response;
  },
  async error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 如果是登录请求的401错误，直接抛出，不进行自动登录
          if (error.config?.url?.includes('/auth/login')) {
            break;
          }
          // 其他401错误，清除认证信息并尝试自动登录
          console.log('401错误，尝试重新登录...');
          clearAuth();
          const loginSuccess = await autoLogin();
          if (!loginSuccess) {
          MessagePlugin.error('登录已过期，请重新登录');
          window.location.href = '/login';
          }
          break;
        case 403:
          MessagePlugin.error('没有权限执行此操作');
          break;
        case 404:
          MessagePlugin.error('请求的资源不存在');
          break;
        case 500:
          MessagePlugin.error('服务器错误，请稍后重试');
          break;
        default:
          MessagePlugin.error(`请求失败: ${error.response.data.message || '未知错误'}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      MessagePlugin.error('网络错误，无法连接到服务器');
    } else {
      // 请求配置出错
      MessagePlugin.error('请求配置错误');
    }
    return Promise.reject(error);
  }
);

export default http; 

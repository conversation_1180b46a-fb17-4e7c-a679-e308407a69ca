<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import { getMainDashboard } from '@/api';

const router = useRouter();

// 治疗头状态数据
const availableHeads = ref(0);
const totalHeads = ref(0);

// 系统信息数据
const systemInfo = ref({
  systemName: 'FREEBONE医疗系统',
  version: '1.0.0',
  uptime: '0小时0分钟'
});

const dataOverview = ref({
  totalPatients: 0,
  totalRecords: 0,
  totalProcesses: 0,
  todayProcesses: 0
});

// 获取主界面数据
const fetchMainData = async () => {
  try {
    const response = await getMainDashboard();
    
    console.log('主界面数据响应:', response);
    
    if (response.data) {
      // 更新治疗头状态
      availableHeads.value = response.data.availableHeads || 18;
      totalHeads.value = response.data.totalHeads || 20;
      
      // 更新系统信息
      if (response.data.systemInfo) {
        systemInfo.value = {
          systemName: response.data.systemInfo.systemName || 'FREEBONE医疗系统',
          version: response.data.systemInfo.version || '1.0.0',
          uptime: response.data.systemInfo.uptime || '0小时0分钟'
        };
      }
      
      // 更新数据概览
      if (response.data.dataOverview) {
        dataOverview.value = {
          totalPatients: response.data.dataOverview.totalPatients || 0,
          totalRecords: response.data.dataOverview.totalRecords || 0,
          totalProcesses: response.data.dataOverview.totalProcesses || 0,
          todayProcesses: response.data.dataOverview.todayProcesses || 0
        };
      }
      
      console.log('主界面数据更新成功:', {
        availableHeads: availableHeads.value,
        totalHeads: totalHeads.value,
        systemInfo: systemInfo.value,
        dataOverview: dataOverview.value
      });
    }
  } catch (error) {
    console.error('获取主界面数据失败:', error);
    // 使用默认值
    availableHeads.value = 18;
    totalHeads.value = 20;
  }
};

// 跳转到新建患者档案页面
const goToNewPatient = () => {
  router.push('/new-patient');
};

// 跳转到患者档案管理页面
const goToPatientManagement = () => {
  router.push('/patients');
};

// 跳转到进程管理页面
const goToProcessManagement = () => {
  router.push('/process-management');
};

// 跳转到治疗头管理页面
const goToTreatmentHeadManagement = () => {
  router.push('/treatment-head-management');
};

// 退出登录
const logout = () => {
  localStorage.removeItem('isAuthenticated');
  MessagePlugin.success('已退出登录');
  router.push('/login');
};

// 在script setup中添加测试函数

// 测试通知功能
const testTreatmentCompletedNotification = () => {
  // 模拟WebSocket消息
  const mockMessage = {
    type: 'TREATMENT_COMPLETED',
    data: {
      patientName: '张三'
    },
    timestamp: new Date().toISOString()
  };
  
  // 直接派发自定义事件
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

const testPickupReminderNotification = () => {
  // 模拟WebSocket消息
  const mockMessage = {
    type: 'PICKUP_REMINDER',
    data: {
      headNumbers: [1, 2, 3]
    },
    timestamp: new Date().toISOString()
  };
  
  // 直接派发自定义事件
  window.dispatchEvent(new CustomEvent('mock-notification', { detail: mockMessage }));
};

// 隐藏开发者模式逻辑
const clickCount = ref(0);
const showDevButtons = ref(false);
let clickTimer: number | null = null;

const handleLogoClick = () => {
  clickCount.value++;
  
  // 清除之前的定时器
  if (clickTimer) {
    clearTimeout(clickTimer);
  }
  
  // 如果连续点击5次，显示开发者按钮
  if (clickCount.value >= 5) {
    showDevButtons.value = true;
    clickCount.value = 0;
    return;
  }
  
  // 设置超时重置计数器（2秒内没有点击就重置）
  clickTimer = setTimeout(() => {
    clickCount.value = 0;
  }, 2000);
};

// 页面加载时获取治疗头状态
onMounted(() => {
  fetchMainData();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (clickTimer) {
    clearTimeout(clickTimer);
  }
});
</script>

<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="box_1 flex-col">
        <!-- 左上角返回按钮 -->
        
        <div class="back-button flex-row justify-between" @click="logout">
          </div>
        
        <!-- 隐藏的开发者测试按钮 (连续点击5次logo显示) -->
        <div v-if="showDevButtons" class="test-buttons" style="position: absolute; top: 20px; right: 200px; z-index: 999;">
          <button @click="testTreatmentCompletedNotification" style="margin-right: 10px; padding: 5px 10px; background: #4CAF50; color: white; border: none; cursor: pointer;">
            测试治疗完成
          </button>
          <button @click="testPickupReminderNotification" style="margin-right: 10px; padding: 5px 10px; background: #2196F3; color: white; border: none; cursor: pointer;">
            测试待取回
          </button>
          <button @click="showDevButtons = false" style="padding: 5px 10px; background: #f44336; color: white; border: none; cursor: pointer;">
            隐藏
          </button>
        </div>

        <!-- 右上角治疗头状态 -->
        <div class="treatment-status" @click="goToTreatmentHeadManagement">
          <span>{{ availableHeads }}/{{ totalHeads }}</span>
        </div>
        
        <img
          class="image_1"
          referrerpolicy="no-referrer"
          src="../assets/images/logo.png"
          @click="handleLogoClick"
        />
        <div class="image-wrapper_1 flex-row justify-between">
          <div class="function-button-container">
            <img
              class="image_2"
              referrerpolicy="no-referrer"
              src="../assets/images/new_patient.png"
              @click="goToNewPatient"
            />
          </div>
          <div class="function-button-container">
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="../assets/images/patient_management.png"
              @click="goToPatientManagement"
            />
          </div>
        </div>
        <div class="function-button-container center">
          <img
            class="image_4"
            referrerpolicy="no-referrer"
            src="../assets/images/process_management.png"
            @click="goToProcessManagement"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  height: 1080px;
  background: url('../assets/images/background.png')
    100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.box_1 {
  width: 1920px;
  height: 1080px;
  background: url('../assets/images/main_background.png')
    0px 0px no-repeat;
  background-size: 1920px 1083px;
  position: relative;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 100px;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  width: 241px;
  height: 70px;
  background: url(../assets/images/返回登录.png)
    0px 0px no-repeat;
  background-size: 197.0699999999997px 61px;
}

.group_11 {
  width: 33px;
  height: 30px;
  margin: 16px 0 0 20px;
}

.text_31 {
  width: 152px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 95px;
  margin: -15px 20px 0 70px;
}
.back-button:hover {
  transform: scale(1.05);
}

.treatment-status {
  position: absolute;
  top: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 30px;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
}

.image_1 {
  width: 691px;
  height: 111px;
  margin: 155px 0 0 615px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image_1:hover {
  transform: scale(1.02);
}

.image-wrapper_1 {
  width: 1304px;
  height: 462px;
  margin: 148px 0 0 271px;
  position: relative;
}

.function-button-container {
  position: relative;
  overflow: visible;
  transition: all 0.3s ease;
}

.function-button-container:hover {
  transform: translateY(-8px);
}

.function-button-container:active {
  transform: translateY(2px);
}

.function-button-container.center {
  margin: 5px 0 62px 786px;
  width: 346px;
}

.image_2 {
  width: 663px;
  height: 462px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_2:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.0);
}

.image_3 {
  width: 611px;
  height: 420px;
  margin-top: 18px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_3:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

.image_4 {
  width: 346px;
  height: 137px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_4:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style>

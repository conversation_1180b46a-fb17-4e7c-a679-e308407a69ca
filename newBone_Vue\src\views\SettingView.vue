<!-- src/views/SettingView.vue -->
<template>
  <div class="page flex-col">
    <div class="box_1 flex-col">
      <div class="group_1 flex-row">
        <div class="block_1 flex-col">
          <div 
            class="image-wrapper_1 flex-row" 
            @click="goToLogin"
          >
            <img
              class="image_1"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/1d55adcbeba78c03a661e95bdec4b8b8.png"
            />
          </div>
          <div class="text-wrapper_1 flex-row"><span class="text_1">设&nbsp;&nbsp;置</span></div>
          <div class="group_2 flex-row">
            <span class="text_2">密码设置:</span>
            <div class="text-wrapper_2 flex-col" @click="showPasswordResetModal = true">
              <span class="text_3">密码重置</span>
            </div>
            <div class="text-wrapper_3 flex-col" @click="showPasswordChangeModal = true">
              <span class="text_4">修改密码</span>
            </div>
          </div>
          <div class="group_3 flex-row">
            <span class="text_5">音量设置:</span>
            <img
              class="image_2"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/b94dac48fdda827b2088f6841c5c98bf.png"
            />
            <div class="volume-slider-container">
              <input 
                type="range" 
                min="0" 
                max="100" 
                v-model="volume" 
                class="volume-slider"
                @input="onVolumeChange"
              />
            </div>
            <img
              class="image_4"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/d803a03d9949ca46fa239e0a7421032a.png"
            />
          </div>
          <div class="group_4 flex-row">
            <span class="text_6">息屏设置:</span>
            <div class="text-wrapper_4 flex-col" @click="toggleScreenTimeSelector">
              <span class="text_7">{{ screenTimeout }}min</span>
            </div>
            <!-- 时间选择器弹窗 -->
            <div v-if="showScreenTimeSelector" class="screen-time-picker-popup">
              <div class="screen-time-picker-container">
                <div 
                  v-for="timeOption in getScreenTimeOptions()" 
                  :key="timeOption.value"
                  class="screen-time-picker-item"
                  :class="{ 
                    'current': timeOption.type === 'current',
                    'near': timeOption.type === 'near',
                    'far': timeOption.type === 'far'
                  }"
                  @click="selectScreenTime(timeOption.value)"
                >
                  {{ timeOption.label }}
                </div>
              </div>
            </div>
            <div class="switch-wrapper">
              <t-switch 
                v-model="screenTimeoutEnabled" 
                @change="onScreenTimeoutToggle"
                :style="{ '--switch-color': '#DBEFDC' }"
              />
            </div>
          </div>
          <div class="group_5 flex-row">
            <span class="text_8">语言设置:</span>
            <div class="text-wrapper_5 flex-col"><span class="text_9">中文</span></div>
            <div class="text-wrapper_6 flex-col"><span class="text_10">ENGLISH</span></div>
          </div>
          <div class="group_6 flex-row">
            <span class="text_11">提醒设置:</span>
            <div class="text-wrapper_7 flex-col" @click="setReminderTime(10)" 
                 :class="{ 'active-reminder': reminderTime === 10 }">
              <span class="text_12">10min</span>
            </div>
            <div class="text-wrapper_8 flex-col" @click="setReminderTime(15)" 
                 :class="{ 'active-reminder': reminderTime === 15 }">
              <span class="text_13">15min</span>
            </div>
            <div class="text-wrapper_9 flex-col" @click="setReminderTime(20)" 
                 :class="{ 'active-reminder': reminderTime === 20 }">
              <span class="text_14">20min</span>
            </div>
          </div>
          <div class="image-wrapper_3 flex-row">
            <img
              class="image_5"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/87946cae3cd6b695bd0468693af11b26.png"
            />
          </div>
       
        </div>
      </div>
      <div class="image-wrapper_4 flex-row">
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          src="../assets/images/settings/7a65d20cdb94191fc58c995ea8c418a2.png"
        />
      </div>
    </div>

    <!-- 密码重置弹窗 -->
    <div v-if="showPasswordResetModal" class="modal-overlay" @click="showPasswordResetModal = false">
      <div class="modal-content" @click.stop>
        <h3>密码重置</h3>
        <p>请输入厂家密码以重置用户密码为123456</p>
        <div class="form-group">
          <label>厂家密码：</label>
          <input 
            type="password" 
            v-model="factoryPassword" 
            placeholder="请输入厂家密码"
            class="modal-input"
          />
        </div>
        <div class="modal-buttons">
          <button class="btn-cancel" @click="showPasswordResetModal = false">取消</button>
          <button class="btn-confirm" @click="resetPassword">确认重置</button>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <div v-if="showPasswordChangeModal" class="modal-overlay" @click="showPasswordChangeModal = false">
      <div class="modal-content" @click.stop>
        <h3>修改密码</h3>
        <div class="form-group">
          <label>原密码：</label>
          <input 
            type="password" 
            v-model="oldPassword" 
            placeholder="请输入原密码"
            class="modal-input"
          />
        </div>
        <div class="form-group">
          <label>新密码：</label>
          <input 
            type="password" 
            v-model="newPassword" 
            placeholder="请输入新密码（至少6位）"
            class="modal-input"
          />
        </div>
        <div class="form-group">
          <label>确认密码：</label>
          <input 
            type="password" 
            v-model="confirmPassword" 
            placeholder="请再次输入新密码"
            class="modal-input"
          />
        </div>
        <div class="modal-buttons">
          <button class="btn-cancel" @click="showPasswordChangeModal = false">取消</button>
          <button class="btn-confirm" @click="changePassword">确认修改</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';

const router = useRouter();

// 音量控制
const volume = ref(50);

// 息屏设置
const screenTimeout = ref(20); // 默认20分钟
const screenTimeoutEnabled = ref(true);
const showScreenTimeSelector = ref(false);

// 提醒设置
const reminderTime = ref(10); // 默认10分钟

// 弹窗控制
const showPasswordResetModal = ref(false);
const showPasswordChangeModal = ref(false);

// 密码表单数据
const factoryPassword = ref('');
const oldPassword = ref('');
const newPassword = ref('');
const confirmPassword = ref('');

// 音量变化处理
const onVolumeChange = () => {
  console.log('当前音量:', volume.value);
  localStorage.setItem('volume', volume.value.toString());
  
  // 调用系统音量设置 - 适配前端应用框架
  if ((window as any).electronAPI) {
    (window as any).electronAPI.setVolume(volume.value);
  }
};

// 密码重置
const resetPassword = async () => {
  if (factoryPassword.value !== '000000') {
    MessagePlugin.error('厂家密码错误');
    return;
  }
  
  try {
    // 调用后端API重置密码
    const response = await fetch('/api/settings/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        factoryPassword: factoryPassword.value
      })
    });
    
    if (response.ok) {
      MessagePlugin.success('密码已重置为123456');
      showPasswordResetModal.value = false;
      factoryPassword.value = '';
    } else {
      MessagePlugin.error('密码重置失败');
    }
  } catch (error) {
    MessagePlugin.error('网络错误，请重试');
  }
};

// 修改密码
const changePassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    MessagePlugin.error('新密码确认不匹配');
    return;
  }
  
  if (newPassword.value.length < 6) {
    MessagePlugin.error('新密码长度至少6位');
    return;
  }
  
  try {
    const response = await fetch('/api/settings/change-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        oldPassword: oldPassword.value,
        newPassword: newPassword.value
      })
    });
    
    if (response.ok) {
      MessagePlugin.success('密码修改成功');
      showPasswordChangeModal.value = false;
      oldPassword.value = '';
      newPassword.value = '';
      confirmPassword.value = '';
    } else {
      const data = await response.json();
      MessagePlugin.error(data.message || '密码修改失败');
    }
  } catch (error) {
    MessagePlugin.error('网络错误，请重试');
  }
};

// 息屏设置
const toggleScreenTimeSelector = () => {
  showScreenTimeSelector.value = !showScreenTimeSelector.value;
};

const getScreenTimeOptions = () => {
  const current = screenTimeout.value;
  const options = [];
  
  // 前面2个时间
  const prev2 = Math.max(5, current - 10);
  const prev1 = Math.max(5, current - 5);
  
  // 后面2个时间
  const next1 = Math.min(120, current + 5);
  const next2 = Math.min(120, current + 10);
  
  options.push({ value: prev2, label: `${prev2}min`, type: 'far' });
  options.push({ value: prev1, label: `${prev1}min`, type: 'near' });
  options.push({ value: current, label: `${current}min`, type: 'current' });
  options.push({ value: next1, label: `${next1}min`, type: 'near' });
  options.push({ value: next2, label: `${next2}min`, type: 'far' });
  
  return options;
};

const selectScreenTime = (minutes: number) => {
  screenTimeout.value = minutes;
  showScreenTimeSelector.value = false;
  saveScreenSettings();
};

const onScreenTimeoutToggle = () => {
  saveScreenSettings();
};

const saveScreenSettings = () => {
  localStorage.setItem('screenTimeout', screenTimeout.value.toString());
  localStorage.setItem('screenTimeoutEnabled', screenTimeoutEnabled.value.toString());
  
  // 调用系统息屏设置
  if ((window as any).electronAPI) {
    (window as any).electronAPI.setScreenTimeout(screenTimeoutEnabled.value ? screenTimeout.value : 0);
  }
};

// 提醒设置
const setReminderTime = (minutes: number) => {
  reminderTime.value = minutes;
  localStorage.setItem('reminderTime', reminderTime.value.toString());
  MessagePlugin.success(`提醒时间已设置为${minutes}分钟`);
};

// 跳转到登录页面
const goToLogin = () => {
  localStorage.removeItem('isAuthenticated');
  router.push('/login');
};

// 组件挂载时读取保存的设置
onMounted(() => {
  // 读取音量设置
  const savedVolume = localStorage.getItem('volume');
  if (savedVolume) {
    volume.value = parseInt(savedVolume, 10);
  }
  
  // 读取息屏设置
  const savedTimeout = localStorage.getItem('screenTimeout');
  if (savedTimeout) {
    screenTimeout.value = parseInt(savedTimeout, 10);
  }
  
  const savedTimeoutEnabled = localStorage.getItem('screenTimeoutEnabled');
  if (savedTimeoutEnabled) {
    screenTimeoutEnabled.value = savedTimeoutEnabled === 'true';
  }
  
  // 读取提醒设置
  const savedReminderTime = localStorage.getItem('reminderTime');
  if (savedReminderTime) {
    reminderTime.value = parseInt(savedReminderTime, 10);
  }
});
</script>

<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1080px;
  background: url('../assets/images/settings/79feb2122d04465638aba851729e875a.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.group_1 {
  width: 1319px;
  height: 779px;
  margin: 116px 0 0 301px;
}

.block_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  height: 779px;
  border: 1px solid rgba(96, 96, 96, 1);
  width: 1319px;
  position: relative;
}

.image-wrapper_1 {
  width: 60px;
  height: 60px;
  margin: 31px 0 0 1216px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-wrapper_1:hover {
  transform: scale(1.1);
}

.image_1 {
  width: 60px;
  height: 60px;
}

.text-wrapper_1 {
  width: 131px;
  height: 48px;
  margin-left: 608px;
}

.text_1 {
  width: 131px;
  height: 48px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41.67px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.group_2 {
  width: 716px;
  height: 61px;
  margin: 50px 0 0 276px;
}

.text_2 {
  width: 209px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 9px;
}

.text-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 55px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-wrapper_2:hover {
  background-color: rgba(200, 200, 200, 1);
}

.text_3 {
  width: 130px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 20px;
}

.text-wrapper_3 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 45px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-wrapper_3:hover {
  background-color: rgba(200, 200, 200, 1);
}

.text_4 {
  width: 130px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 20px;
}

.group_3 {
  width: 689px;
  height: 46px;
  margin: 41px 0 0 276px;
  display: flex;
  align-items: center;
}

.text_5 {
  width: 208px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.image_2 {
  width: 50px;
  height: 45px;
  margin-left: 116px;
}

.volume-slider-container {
  width: 220px;
  height: 3px;
  margin: 0 26px;
  position: relative;
  background: #ddd;
}

.volume-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 100%;
  background: transparent;
  outline: none;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 40px;
  border-radius: 6px; /* 改为圆角矩形 */
  background: #EF8029;
  cursor: pointer;
  border: 2px solid #878482;
  position: relative;
  z-index: 2;
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 40px;
  border-radius: 6px; /* 改为圆角矩形 */
  background: #EF8029;
  cursor: pointer;
  border: 2px solid #878482;
}


.image_4 {
  width: 50px;
  height: 45px;
  margin: 1px 0 0 19px;
}

.group_4 {
  width: 618px;
  height: 61px;
  margin: 45px 0 0 276px;
}

.text_6 {
  width: 209px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
}

.text-wrapper_4 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 55px;
}

.text_7 {
  width: 123px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 26px;
}

.image-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 15px;
  height: 41px;
  width: 97px;
  margin: 11px 0 0 67px;
}

.label_1 {
  width: 33px;
  height: 33px;
  margin: 5px 0 0 53px;
}

.group_5 {
  width: 672px;
  height: 61px;
  margin: 32px 0 0 276px;
}

.text_8 {
  width: 208px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 0px;
}

.text-wrapper_5 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 55px;
}

.text_9 {
  width: 71px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 53px;
}

.text-wrapper_6 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 48px;
}

.text_10 {
  width: 127px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 29px;
  margin: 20px 0 0 25px;
}

.group_6 {
  width: 896px;
  height: 61px;
  margin: 34px 0 0 276px;
}

.text_11 {
  width: 208px;
  height: 41px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 3px;
}

.text-wrapper_7 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 55px;
}

.text_12 {
  width: 121px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 28px;
}

.text-wrapper_8 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 46px;
}

.text_13 {
  width: 121px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 28px;
}

.text-wrapper_9 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 53px;
}

.text_14 {
  width: 123px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 26px;
}

.image-wrapper_3 {
  width: 160px;
  height: 25px;
  margin: 33px 0 50px 599px;
}

.image_5 {
  width: 160px;
  height: 25px;
}



.image-wrapper_4 {
  width: 138px;
  height: 50px;
  margin: 91px 0 44px 1682px;
}

.image_6 {
  width: 138px;
  height: 50px;
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 10px;
  padding: 30px;
  width: 450px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #333;
  text-align: center;
  font-family: MicrosoftYaHei;
}

.modal-content p {
  margin: 0 0 20px 0;
  color: #666;
  text-align: center;
  font-family: MicrosoftYaHei;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  color: #333;
  font-family: MicrosoftYaHei;
}

.modal-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  font-family: MicrosoftYaHei;
  box-sizing: border-box;
}

.modal-input:focus {
  outline: none;
  border-color: #FF8C00;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 30px;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-family: MicrosoftYaHei;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.btn-confirm {
  background-color: #FFE4B5;
  color: #D2691E;
  border: 1px solid #FF8C00;
}

.btn-confirm:hover {
  background-color: #FFECD1;
}

/* 激活状态样式 */
.active {
  background-color: #FFE4B5 !important;
  border-color: #FF8C00 !important;
}

.active-reminder {
  background-color: #FFE4B5 !important;
  border-color: #FF8C00 !important;
}

.active-reminder .text_12,
.active-reminder .text_13,
.active-reminder .text_14 {
  color: #D2691E !important;
}

/* 息屏时间选择器样式 */
.screen-time-picker-popup {
  position: absolute;
  top: 315px;
  left: 520px;
  z-index: 1000;
  background: white;
  width: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.screen-time-picker-container {
  padding: 10px 0;
}

.screen-time-picker-item {
  padding: 12px 20px;
  cursor: pointer;
  text-align: center;
  font-family: MicrosoftYaHei;
  font-size: 16px;
  transition: all 0.3s ease;
  min-width: 80px;
}

.screen-time-picker-item:hover {
  background-color: #FFECD1;
}

.screen-time-picker-item.current {
  background-color: #FFE4B5;
  color: #D2691E;
  font-weight: bold;
  border: 1px solid #FF8C00;
}

.screen-time-picker-item.near {
  background-color: #f8f8f8;
  color: #333;
}

.screen-time-picker-item.far {
  background-color: white;
  color: #666;
}

.switch-wrapper {
  margin-left: 67px;
  margin-top: 11px;
}

/* TDesign Switch 自定义样式 */
.switch-wrapper :deep(.t-switch) {
  --td-switch-color: #FF8C00 !important;
  width: 70px;
  height: 30px;
}

.switch-wrapper :deep(.t-switch.t-is-checked) {
  background-color: #FFE4B5 !important;
}

.switch-wrapper :deep(.t-switch .t-switch__handle) {
  width: 26px !important;
  height: 26px !important;
  background-color: white !important;
  top: 2px !important;
  left: 2px !important;
}

.switch-wrapper :deep(.t-switch.t-is-checked .t-switch__handle) {
  background-color: white !important;
  transform: translateX(40px) !important; /* 70px - 26px - 4px = 40px */
}
</style> 
<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="box_1 flex-col">
        <div class="block_1 flex-row justify-between">
          <div class="back-button">返回</div>
          <span class="text_1">个人信息测试页面</span>
        </div>
        <div class="block_2 flex-row justify-between">
          <div class="box_2 flex-row">
            <div class="block_3 flex-col">
              <!-- 头像占位 -->
            </div>
            <div class="block_4 flex-col">
              <div class="box_4 flex-col"></div>
              <span class="text_3">姓名：李u</span>
              <span class="text_4">性别：男</span>
              <span class="text_6">年龄：52</span>

              <div class="text-wrapper_1 flex-row">
                <span class="text_5">编号：P001</span>
                <span class="text_7">电话：13800138006</span>
              </div>
              <div class="text-wrapper_2 flex-row justify-between">
                <span class="text_8">就诊卡号：P20250103006</span>
              </div>
            </div>
            <div class="block_5 flex-col">
              <div class="section_1 flex-col">
                <div class="text-wrapper_4 flex-col">
                  <span class="text_14">诊断详情</span>
                </div>
              </div>
            </div>
          </div>
          <div class="box_5 flex-col">
            <div class="text-wrapper_5 flex-row">
              <span class="text_15">肩颈部：</span>
              <span class="text_16">30min</span>
              <span class="text_17">上肢：</span>
              <span class="text_18">15min</span>
            </div>
            <div class="text-wrapper_6 flex-row">
              <span class="text_19">腰背部：</span>
              <span class="text_20">45min</span>
              <span class="text_21">下肢：</span>
              <span class="text_22">20min</span>
            </div>
            <div class="text-wrapper_7 flex-row">
              <span class="text_23">其他部位：</span>
              <span class="text_24">10min</span>
              <span class="text_25">髋部：</span>
              <span class="text_26">25min</span>
            </div>
            <div class="group_2 flex-row">
              <div class="text-wrapper_8 flex-col">
                <span class="text_27">治疗时间合计：145min</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 测试页面，无需复杂逻辑
</script>

<style scoped lang="css">
/* 使用修复后的样式 */
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  height: 1078px;
  background-color: #f5f5f5;
  width: 1917px;
  position: relative;
}

.box_1 {
  position: absolute;
  width: 1920px;
  height: 1080px;
  background-color: white;
}

.block_1 {
  width: 1004px;
  height: 70px;
  margin: 18px 0 0 100px;
}

.back-button {
  width: 169px;
  height: 70px;
  cursor: pointer;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 20px;
}

.text_1 {
  width: 400px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 49px;
  margin: 0 auto;
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
}

.block_2 {
  width: 1627px;
  height: 275px;
  margin: 70px 0 0 136px;
  display: flex;
  justify-content: space-between;
}

.box_2 {
  width: 941px;
  height: 273px;
  background-color: #f8f9fa;
  border: 2px solid #dee2e6;
  border-radius: 10px;
  position: relative;
}

.block_3 {
  background-color: rgba(239, 239, 239, 1);
  border-radius: 50%;
  width: 155px;
  height: 155px;
  border: 1px solid rgba(203, 203, 203, 1);
  position: absolute;
  top: 30px;
  left: 30px;
}

.block_4 {
  position: absolute;
  width: 600px;
  height: 156px;
  top: 30px;
  left: 200px;
}

.text_3, .text_4, .text_5, .text_6, .text_7, .text_8 {
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin-bottom: 10px;
}

.text_3 {
  position: absolute;
  top: 0;
  left: 0;
}

.text_4 {
  position: absolute;
  top: 0;
  left: 200px;
}

.text_6 {
  position: absolute;
  top: 0;
  left: 400px;
}

.text-wrapper_1 {
  position: absolute;
  top: 40px;
  left: 0;
  width: 500px;
  display: flex;
}

.text_7 {
  margin-left: 100px;
}

.text-wrapper_2 {
  position: absolute;
  top: 80px;
  left: 0;
}

.block_5 {
  position: absolute;
  top: 150px;
  right: 50px;
}

.section_1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.text_14 {
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  letter-spacing: 10px;
}

.box_5 {
  height: 275px;
  background-color: #e3f2fd;
  border: 2px solid #2196f3;
  border-radius: 10px;
  width: 636px;
  align-self: flex-start;
  position: relative;
  padding: 50px 68px;
}

.text-wrapper_5, .text-wrapper_6, .text-wrapper_7 {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.text_15, .text_16, .text_17, .text_18, .text_19, .text_20, .text_21, .text_22, .text_23, .text_24, .text_25, .text_26 {
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  line-height: 25px;
  margin-right: 20px;
}

.text_27 {
  color: rgba(60, 60, 60, 1);
  font-size: 28px;
  font-family: MicrosoftYaHei;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  margin-top: 20px;
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style>

<template>
  <div class="page flex-col">
    <div class="block_1 flex-col" :style="{ backgroundImage: `url(${backgroundImg})` }">
      <div class="group_1 flex-col">
        <div class="image-wrapper_1 flex-row justify-between">
          <img
            class="image_1"
            :src="backButtonImg"
            @click="goBack"
            alt="返回"
          />
          <img
            class="image_2"
            :src="titleImg"
            alt="标题"
          />
        </div>
        <div class="group_2 flex-row">
          <div class="user-avatar-wrapper flex-col" :style="{ backgroundImage: `url(${wrapper2Img})` }">
            <img
              class="user-avatar"
              :src="userAvatarImg"
              alt="用户头像"
            />
          </div>
          <div class="user-name-container flex-row">
            <img
              class="user-name-placeholder"
              :src="userNamePlaceholderImg"
              alt="姓名占位图"
            />
            <span class="patient-name">{{ patientName }}</span>
          </div>
          <img
            class="new-record-button"
            :src="newRecordButtonImg"
            @click="goToNewRecord"
            alt="返回个人新建"
          />
          <img
            class="process-management-button"
            :src="processManagementButtonImg"
            @click="goToProcessManagement"
            alt="查看进程管理"
          />
        </div>
        <div class="group_3 flex-row">
          <div 
            v-for="(head, index) in treatmentHeads" 
            :key="head.id"
            class="treatment-head-box flex-col" 
            :class="`treatment-head-${index + 1}`"
            :style="{ backgroundImage: `url(${getBoxBackgroundImage(index)})` }"
          >
            <div class="treatment-head-header flex-row justify-between">
              <div class="body-part-label">
                <span class="body-part-text">{{ head.headNumber }}号-{{ head.bodyPart }}</span>
              </div>
              <img
                class="close-treatment-head-button"
                :src="closeTreatmentHeadButtonImg"
                @click="closeTreatmentHead(head.id)"
                alt="关闭治疗头"
              />
            </div>
            <div class="remaining-time-row flex-row justify-between">
              <img
                class="remaining-time-label"
                :src="remainingTimeLabelImg"
                alt="剩余时间"
              />
              <div class="remaining-time-value">
                <span class="time-text">{{ formatTime(head.remainingTimeSeconds) }}</span>
              </div>
            </div>
            <div class="intensity-row flex-row justify-between">
              <img
                class="intensity-label"
                :src="intensityLabelImg"
                alt="治疗声强"
              />
              <div class="intensity-value">
                <span class="intensity-text">{{ head.intensity }}</span>
              </div>
            </div>
          </div>
        </div>
        <img
          class="bottom-info"
          :src="bottomInfoImg"
          @click="closeAllTreatmentHeads"
          alt="结束治疗"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

// 头部区域图片资源
import backButtonImg from '@/assets/images/treatmentprocess/back-button.png';
import titleImg from '@/assets/images/treatmentprocess/treatment-process-title.png';

// 控制区域图片资源
import userAvatarImg from '@/assets/images/treatmentprocess/user-avatar.png';
import userNamePlaceholderImg from '@/assets/images/treatmentprocess/user-name-placeholder.png';
import newRecordButtonImg from '@/assets/images/treatmentprocess/new-record-button.png';
import processManagementButtonImg from '@/assets/images/treatmentprocess/process-management-button.png';

// 治疗头通用图片资源
import bodyPartPlaceholderImg from '@/assets/images/treatmentprocess/body-part-placeholder.png';
import closeTreatmentHeadButtonImg from '@/assets/images/treatmentprocess/close-treatment-head-button.png';
import remainingTimeLabelImg from '@/assets/images/treatmentprocess/remaining-time-label.png';
import remainingTimeValueImg from '@/assets/images/treatmentprocess/remaining-time-value.png';
import intensityLabelImg from '@/assets/images/treatmentprocess/intensity-label.png';
import intensityValueImg from '@/assets/images/treatmentprocess/intensity-value.png';

// 其他占位图片（暂时保留原来的导入名）
import image11Img from '@/assets/images/treatmentprocess/ps3x2emjtod3d3qchuo7f5vbd2w5husn7orac8cbf5b-20d2-4f31-a46d-4e2f8625d2a5.png';
import label4Img from '@/assets/images/treatmentprocess/pssa663bnxu4pntf8gnag1v773dlpi87idtc53d9809-a257-407f-b46b-6b7b1181137a.png';
import image12Img from '@/assets/images/treatmentprocess/psek6kq4v6846axq2a5qwfmkr5qwrste197766e9d3-2aa7-485c-a55d-e09b339de113.png';
import image13Img from '@/assets/images/treatmentprocess/ps1dhefhsn9eucl3t7zqgn18kk2zy79fgum03e47201-8eee-49c1-a591-1f55f1623bda.png';
import image14Img from '@/assets/images/treatmentprocess/psyzk5p3oubwewuiwhz4aoytqzo9vmdlua79141f6-5c82-42af-ae80-59b2f91d87ff.png';
import image15Img from '@/assets/images/treatmentprocess/ps94i3a3nzhdrj8e3jdcem9soxhc0j92870a408fc4-2aee-4fff-ab04-0a6ee003e927.png';
import image16Img from '@/assets/images/treatmentprocess/ps47u7v7cow7548nohj9wl8mzc47hbq1j314e05d61-6b69-4742-84ca-a0ac553d483e.png';
import label5Img from '@/assets/images/treatmentprocess/ps3dpzhpn9i07wshqsf264h877nuvxde4vdc17eb293-9772-4bba-bad3-a2b18bfbcdbb.png';
import image17Img from '@/assets/images/treatmentprocess/psz20qhmybnwbrrdsffvrchz4ys2i0e1w3bada7bb-7a53-40b7-a283-dc88bfea4afa.png';
import image18Img from '@/assets/images/treatmentprocess/ps171djdnrpjxzhoevknu2kqkezs32l9frc1973b6b-985a-4452-87ab-f68ca19e8fae.png';
import image19Img from '@/assets/images/treatmentprocess/pswv0wq3eyfxezyi09ksuo67trgiiqw2s71304d0ad-a70c-4806-82ea-a46864f7f5d3.png';
import image20Img from '@/assets/images/treatmentprocess/ps8dl4zpv7xlbktsisxau3aoin8ft02bp5k262323aa-7e0a-4592-9577-2c930486e7c1.png';

// 底部图片
import bottomInfoImg from '@/assets/images/treatmentprocess/bottom-info.png';

// Background image
import backgroundImg from '@/assets/images/treatmentprocess/main-background.png';

// Box background images
import box1Img from '@/assets/images/treatmentprocess/treatment-box-1-bg.png';
import box2Img from '@/assets/images/treatmentprocess/treatment-box-2-bg.png';
import box3Img from '@/assets/images/treatmentprocess/treatment-box-3-bg.png';

// Wrapper background images
import wrapper2Img from '@/assets/images/treatmentprocess/user-avatar-wrapper-bg.png';

const router = useRouter();
const route = useRoute();

// 患者信息
const patientName = ref('张三'); // 模拟患者姓名，实际应从路由参数或存储中获取

// 治疗头数据状态
interface TreatmentHead {
  id: string;
  headNumber: string;  // 治疗头编号
  bodyPart: string;    // 对应的治疗部位
  remainingTimeSeconds: number;  // 剩余时间（秒）
  intensity: string;   // 治疗声强
  isActive: boolean;   // 是否活跃
}

const treatmentHeads = ref<TreatmentHead[]>([]);

// 根据治疗设置生成治疗头数据
const generateTreatmentHeadsFromSettings = () => {
  // 模拟从治疗设置页面传递的数据或从localStorage获取
  // 实际项目中应该从路由参数、全局状态或后端接口获取
  const mockTreatmentSettings = [
    {
      name: '肩颈部',
      time: '15分钟',
      intensity: '150',
      frequency: '1000',
      depth: '深部',
      count: '2'  // 用户选择的治疗头数量
    },
    {
      name: '腰背部',
      time: '12分钟',
      intensity: '180',
      frequency: '1000',
      depth: '深部',
      count: '3'  // 用户选择的治疗头数量
    },
    {
      name: '下肢',
      time: '8分钟',
      intensity: '160',
      frequency: '100',
      depth: '浅部',
      count: '1'  // 用户选择的治疗头数量
    }
  ];

  const treatmentHeads: TreatmentHead[] = [];
  let headCounter = 1;

  mockTreatmentSettings.forEach(setting => {
    const count = parseInt(setting.count) || 1;
    const timeInMinutes = parseInt(setting.time) || 15;
    const timeInSeconds = timeInMinutes * 60 + Math.floor(Math.random() * 60); // 添加随机秒数

    // 根据选择的数量生成对应数量的治疗头
    for (let i = 0; i < count; i++) {
      treatmentHeads.push({
        id: headCounter.toString(),
        headNumber: headCounter.toString().padStart(2, '0'),
        bodyPart: setting.name,
        remainingTimeSeconds: timeInSeconds,
        intensity: `${setting.intensity}mW/cm²`,
        isActive: true
      });
      headCounter++;
    }
  });

  return treatmentHeads;
};

// 倒计时定时器
let countdownTimer: number | null = null;

// 初始化治疗数据
const initializeTreatmentData = () => {
  // 实际项目中应该从参数设置页面传递的数据或接口获取
  treatmentHeads.value = generateTreatmentHeadsFromSettings();
  
  // 模拟从路由参数获取患者信息
  const patientId = route.params.patientId;
  console.log('患者ID:', patientId);
  
  // 实际项目中应该根据patientId获取患者信息
  // 这里使用模拟数据
};

// 关闭指定治疗头
const closeTreatmentHead = (headId: string) => {
  const index = treatmentHeads.value.findIndex(head => head.id === headId);
  if (index !== -1) {
    treatmentHeads.value[index].isActive = false;
    console.log(`关闭治疗头: ${treatmentHeads.value[index].bodyPart}`);
    
    // 移除不活跃的治疗头
    treatmentHeads.value = treatmentHeads.value.filter(head => head.isActive);
  }
};

// 根据索引获取治疗头背景图片
const getBoxBackgroundImage = (index: number) => {
  const backgroundImages = [box1Img, box2Img, box3Img];
  return backgroundImages[index % backgroundImages.length];
};

// 将秒数转换为MM:SS格式
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 开始倒计时
const startCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
  
  countdownTimer = setInterval(() => {
    treatmentHeads.value.forEach(head => {
      if (head.isActive && head.remainingTimeSeconds > 0) {
        head.remainingTimeSeconds--;
        
        // 当倒计时到0时
        if (head.remainingTimeSeconds <= 0) {
          console.log(`治疗头 ${head.bodyPart} 治疗完成`);
          // 这里可以添加治疗完成的处理逻辑
        }
      }
    });
    
    // 检查是否所有治疗头都完成了
    const activeHeads = treatmentHeads.value.filter(head => head.isActive && head.remainingTimeSeconds > 0);
    if (activeHeads.length === 0) {
      stopCountdown();
      console.log('所有治疗头治疗完成');
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 跳转到新建档案
const goToNewRecord = () => {
  router.push('/new-patient');
};

// 跳转到进程管理
const goToProcessManagement = () => {
  router.push('/patients');
};

// 关闭所有治疗头
const closeAllTreatmentHeads = () => {
  console.log('关闭所有治疗头');
  treatmentHeads.value = [];
};

// 页面加载时初始化数据
onMounted(() => {
  initializeTreatmentData();
  startCountdown();
});

// 页面卸载时清理定时器
onUnmounted(() => {
  stopCountdown();
});
</script>

<style scoped>
/* 完全按照原始蓝湖设计的样式 */
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  margin: 0 auto;
}

.block_1 {
  height: 1080px;
  background-repeat: no-repeat;
  background-size: 1920px 1080px;
  background-position: 0px 0px;
  margin-left: 16px;
  width: 1904px;
}

.group_1 {
  height: 1080px;
  margin-left: -16px;
  width: 1920px;
  position: relative;
}

/* 顶部区域 */
.image-wrapper_1 {
  width: 948px;
  height: 61px;
  margin: 20px 0 0 112px;
}

.image_1 {
  width: 151px;
  height: 61px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image_1:hover {
  transform: scale(1.05);
}

.image_2 {
  width: 200px;
  height: 47px;
  margin-top: 4px;
}

/* 中间控制区域 */
.group_2 {
  width: 1575px;
  height: 125px;
  margin: 151px 0 0 108px;
}

.user-avatar-wrapper {
  height: 56px;
  width: 57px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 100%;
}

.user-avatar {
  width: 34px;
  height: 34px;
  margin: 12px 0 0 11px;
}

.user-name-container {
  width: 250px;  /* 适当减少宽度，为右侧按钮留出空间 */
  height: 32px;
  margin: 13px 0 0 12px;
  align-items: center;
  flex-shrink: 0;  /* 防止被压缩 */
}

.user-name-placeholder {
  width: 80px;
  height: 32px;
  flex-shrink: 0;  /* 防止占位图被压缩 */
}

.patient-name {
  font-size: 26px;  /* 接近原图片高度32px */
  font-weight: bold;
  color: #333;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  margin-left: 8px;
  height: 32px;
  line-height: 32px;
  display: inline-block;
  white-space: nowrap;  /* 防止文字换行 */
  overflow: hidden;     /* 隐藏溢出部分 */
  text-overflow: ellipsis;  /* 超长显示省略号 */
  max-width: 200px;     /* 限制最大宽度 */
}

.new-record-button {
  width: 347px;
  height: 115px;
  margin: 10px 0 0 476px;  /* 调整左边距，使其更靠近原设计位置 */
  cursor: pointer;
  transition: transform 0.2s ease;
}

.new-record-button:hover {
  transform: scale(1.05);
}

.process-management-button {
  width: 333px;
  height: 101px;
  margin: 10px 0 0 20px;  /* 统一margin-top为10px，与返回个人新建按钮对齐 */
  cursor: pointer;
  transition: transform 0.2s ease;
}

.process-management-button:hover {
  transform: scale(1.05);
}

/* close-all样式已移除，现在使用bottom-info作为结束按钮 */

/* 主要内容区域 */
.group_3 {
  width: 1767px;
  min-height: 344px;  /* 改为最小高度，允许扩展 */
  margin: 2px 0 50px 77px;  /* 减少底部margin，留给换行空间 */
  flex-wrap: wrap;  /* 允许换行显示 */
  gap: 20px;  /* 治疗头之间的间距 */
}

/* 治疗头盒子样式 */
.treatment-head-box {
  height: 344px;
  width: 563px;
  max-width: calc(33.33% - 20px);  /* 确保一行最多3个 */
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 100%;
  position: relative;  /* 为内部定位提供参考 */
}

/* 治疗头内部元素样式 */
.treatment-head-header {
  width: 480px;  /* 稍微增加宽度 */
  height: 33px;
  margin: 44px 0 0 40px;  /* 调整左边距，确保按钮在框内 */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.body-part-label {
  width: 280px;  /* 进一步增加宽度适应"01号-颈部"格式 */
  height: 27px;
  margin-top: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.body-part-text {
  font-size: 30px;  /* 用户要求改成30px */
  font-weight: bold;
  color: #2c5aa0;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  height: 27px;
  width: 210px;
  line-height: 27px;
  white-space: nowrap;  /* 防止文字换行 */
  text-align: right;
}

.close-treatment-head-button {
  width: 33px;
  height: 33px;
  cursor: pointer;
  transition: transform 0.2s ease;
  flex-shrink: 0;  /* 防止按钮被压缩 */
  margin-left: auto;  /* 推到右边 */
}

.close-treatment-head-button:hover {
  transform: scale(1.1);
}

.remaining-time-row {
  width: 373px;
  height: 33px;
  margin: 91px 0 0 101px;
}

.remaining-time-label {
  width: 133px;
  height: 33px;
}

.remaining-time-value {
  width: 200px;  /* 增加宽度避免文字垂直显示 */
  height: 32px;
  margin-top: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-text {
  font-size: 28px;  /* 匹配原图片145px宽度 */
  font-weight: bold;
  color: #e74c3c;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  height: 32px;
  line-height: 32px;
  white-space: nowrap;  /* 防止文字换行 */
}

.intensity-row {
  width: 420px;
  height: 32px;
  margin: 25px 0 86px 101px;
}

.intensity-label {
  width: 135px;
  height: 32px;
}

.intensity-value {
  width: 250px;  /* 增加宽度避免文字垂直显示 */
  height: 29px;
  margin-top: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.intensity-text {
  font-size: 24px;  /* 匹配原图片192px宽度 */
  font-weight: bold;
  color: #27ae60;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  height: 29px;
  line-height: 29px;
  white-space: nowrap;  /* 防止文字换行 */
}

/* 治疗头盒子间距调整 */
.treatment-head-1 {
  margin-left: 0;
}

/* 移除individual margin，使用flex gap控制间距 */
.treatment-head-1,
.treatment-head-2,
.treatment-head-3,
.treatment-head-4,
.treatment-head-5,
.treatment-head-6 {
  margin-left: 0;
  width: 563px;  /* 统一宽度 */
}



/* 底部图片 - 结束所有治疗头按钮 */
.bottom-info {
  position: absolute;
  left: 1595px;
  top: 236px;
  width: 241px;
  height: 117px;
  cursor: pointer;  /* 显示手型光标 */
  transition: transform 0.2s ease, opacity 0.2s ease;  /* 添加过渡效果 */
}

.bottom-info:hover {
  transform: scale(1.05);  /* 适当的放大效果 */
  opacity: 0.9;  /* 轻微透明效果 */
}

/* Flexbox 布局工具类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style> 
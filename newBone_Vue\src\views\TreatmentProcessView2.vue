<template>
    <div class="page flex-col">
      <div class="group_1 flex-col">
        <div class="block_1 flex-row justify-between">
          <img
            class="image_1"
            referrerpolicy="no-referrer"
            src="../assets/images/treatmentsettingsview/img/ps0rbopt6gpo79v6n1nus8nyhtdkihjzd4vl8a5a6d1c-9d09-4ddd-a32d-e212ee18a676.png"
            @click="goBack"
          />
          <span class="text_1">治疗进程</span>
        </div>
        <div class="block_2 flex-row">
          <img
            class="image_2"
            referrerpolicy="no-referrer"
            src="../assets/images/treatmentprocess/9541918c005334599a1dd4a8efde1d65.png"
          />
          <span class="text_2">姓名：{{ patientName }}</span>
          <div class="group_2 flex-row button" @click="goToNewRecord">
            <img
              class="label_1"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/0edae6f606d6f1ab381b509e03470848.png"
            />
            <span class="text_3">返回个人新建</span>
          </div>
          <div class="group_3 flex-row button" @click="goToProcessManagement">
            <img
              class="label_2"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/0edae6f606d6f1ab381b509e03470848.png"
            />
            <span class="text_4">查看进程管理</span>
          </div>
          <div class="group_4 flex-row button disabled-button">
            <!-- 图片已包含图标和文字，隐藏原有元素 -->
            <img
              class="label_3 hidden"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/71054227f9f833ec4c2433447f86b19d.png"
            />
            <span class="text_6 hidden">结束</span>
          </div>
        </div>
        <div class="block_3 flex-row justify-between">
          <!-- 治疗卡片 -->
          <div 
            class="treatment-card flex-col" 
            v-for="(group, index) in groupedTreatmentDetails" 
            :key="group.bodyPart"
            :class="`card-${index + 1}`"
          >
            <div class="card-header flex-row">
              <div class="image-text flex-row justify-between">
                <img
                  class="label"
                  referrerpolicy="no-referrer"
                  src="../assets/images/treatmentprocess/132e26419509ac28402daef983020f0b.png"
                />
                <span class="text-group">{{ group.bodyPart }} ({{ group.headCount }}个)</span>
              </div>
              <!-- 取走治疗没有结束小图标 -->
            </div>
            <div class="time-row flex-row justify-between">
              <span class="text_7">使用时间</span>
              <span class="text_8">{{ formatTime(group.totalElapsedTime) }}</span>
            </div>
            <img
              class="divider"
              src="../assets/images/treatmentprocess/32eca4f80934b93ed8a88521c5f685a3.png"
              alt="分隔线"
            />
            <div class="intensity-row flex-row justify-between">
              <span class="text_9">治疗声强</span>
              <span class="text_10">{{ group.averageIntensity }}mW/cm²</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { MessagePlugin } from 'tdesign-vue-next';
  import { getTreatmentProcess, terminateProcess } from '@/api';
  import http from '@/utils/axios'; // 添加axios导入
  
  const router = useRouter();
  const route = useRoute();
  
  // 患者信息
  const patientName = ref('');
  const processId = ref<number>(0);
  
  // 治疗详情数据状态
  interface TreatmentDetail {
    bodyPart: string;
    remainingTime: string;
    intensity: string;
    elapsedTimeSeconds: number; // 前端计算的已使用时间（秒）
  }
  
  const treatmentDetails = ref<TreatmentDetail[]>([]);
  const processStatus = ref('');

  // 按部位分组的治疗详情
  const groupedTreatmentDetails = computed(() => {
    const groups = new Map<string, TreatmentDetail[]>();
    
    treatmentDetails.value.forEach(detail => {
      if (!groups.has(detail.bodyPart)) {
        groups.set(detail.bodyPart, []);
      }
      groups.get(detail.bodyPart)!.push(detail);
    });
    
    // 转换为数组格式，每个部位一个对象
    return Array.from(groups.entries()).map(([bodyPart, details]) => ({
      bodyPart,
      details,
      // 修复：同一部位的多个治疗头时间不应叠加，显示最大时间
      totalElapsedTime: Math.max(...details.map(d => d.elapsedTimeSeconds)),
      averageIntensity: extractIntensityValue(details[0].intensity), // 使用第一个治疗头的强度
      headCount: details.length
    }));
  });

  // 从强度字符串中提取数值（如"45mW/C" -> 45）
  const extractIntensityValue = (intensityStr: string): number => {
    const match = intensityStr.match(/(\d+)/);
    return match ? parseInt(match[1]) : 0;
  };
  
  // 获取治疗进程数据时计算准确的已用时间
  const fetchTreatmentProcess = async () => {
    try {
      if (!processId.value) return;
      
      const response = await http.get(`/processes/${processId.value}/realtime`);
      const data = response.data;
      
      patientName.value = data.patientName;
      
      treatmentDetails.value = data.bodyParts.map((bodyPart: any) => ({
        bodyPart: bodyPart.bodyPart,
        remainingTime: bodyPart.remainingTime,
        intensity: bodyPart.intensity,
        elapsedTimeSeconds: bodyPart.elapsedTimeSeconds || 0 // 直接使用后端计算的精确秒数
      }));
      
      console.log('治疗进程实时数据:', data);
    } catch (error) {
      console.error('获取治疗进程失败:', error);
      MessagePlugin.error('获取治疗进程失败');
    }
  };
  
  // 获取卡片图标
  const getCardIcon = (index: number) => {
    const icons = [
      '../assets/images/treatmentprocess/9541918c005334599a1dd4a8efde1d65.png',
      '../assets/images/treatmentprocess/9541918c005334599a1dd4a8efde1d65.png',
      '../assets/images/treatmentprocess/9541918c005334599a1dd4a8efde1d65.png'
    ];
    return icons[index % icons.length];
  };
  
  // 正计时定时器
  let countupTimer: number | null = null;
  
  // 初始化治疗数据
  const initializeTreatmentData = async () => {
    // 从路由参数获取进程ID
    processId.value = parseInt(route.params.processId as string);
    
    if (processId.value) {
      await fetchTreatmentProcess();
    }
  };
  
  // 将秒数转换为MM分SS秒格式
  const formatTime = (seconds: number): string => {
    if (seconds === undefined || seconds < 0) return '0分0秒';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };
  
  // 开始正计时
  const startCountup = () => {
    if (countupTimer) {
      clearInterval(countupTimer);
    }
    
    countupTimer = setInterval(() => {
      treatmentDetails.value.forEach(detail => {
        detail.elapsedTimeSeconds++;
      });
    }, 1000);
  };
  
  // 停止计时
  const stopCountup = () => {
    if (countupTimer) {
      clearInterval(countupTimer);
      countupTimer = null;
    }
  };
  
  // 返回上一页
  const goBack = () => {
    router.back();
  };
  
  // 跳转到新建档案
  const goToNewRecord = () => {
    router.push('/new-patient');
  };
  
  // 跳转到进程管理
  const goToProcessManagement = () => {
    router.push('/process-management');
  };
  
  // 关闭所有治疗头
  const closeAllTreatmentHeads = async () => {
    try {
      await terminateProcess(processId.value);
      treatmentDetails.value = [];
      console.log('关闭所有治疗头');
      MessagePlugin.success('已结束所有治疗');
      router.push('/process-management');
    } catch (error) {
      console.error('结束治疗进程失败:', error);
      MessagePlugin.error('结束治疗进程失败');
    }
  };
  
  // 页面加载时初始化数据
  onMounted(() => {
    initializeTreatmentData().then(() => {
      startCountup();
    });
  });
  
  // 页面卸载时清理定时器
  onUnmounted(() => {
    stopCountup();
  });
  </script>
  
  <style scoped lang="css">
  .page {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
  }
  
  .group_1 {
    height: 1080px;
    background: url(../assets/images/treatmentprocess/581596b6024a8797eb084676c601cc04.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin-left: 1px;
    width: 1919px;
  }
  
  .block_1 {
    width: 1003px;
    height: 70px;
    margin: 20px 0 0 103px;
  }
  
  .image_1 {
    width: 151px;
    height: 61px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .image_1:active {
    transform: scale(0.95);
  }
  
  .text_1 {
    width: 294px;
    height: 50px;
    overflow-wrap: break-word;
    color: rgba(1, 1, 1, 1);
    font-size: 50px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 10px;
  }
  
  .block_2 {
    position: relative;
    width: 1695px;
    height: 92px;
    margin: 77px 0 0 112px;
  }
  
  .image_2 {
    width: 56px;
    height: 56px;
    margin-top: 16px;
  }
  
  .text_2 {
    width: auto;
    height: 34px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 30px 0 0 24px;
  }
  
  .group_2 {
    width: 345px;
    height: 91px;
    background: url(../assets/images/treatmentprocess/da69ab339dafb93891d48a37c09b9a2f.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin-left: 470px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .group_2:active {
    transform: scale(0.95);
  }
  
  .label_1 {
    width: 32px;
    height: 29px;
    margin: 24px 0 0 25px;
  }
  
  .text_3 {
    width: 215px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 15px 49px 0 24px;
  }
  
  .group_3 {
    width: 345px;
    height: 91px;
    background: url(../assets/images/treatmentprocess/f328c43c1f4187c31ec25f2a47113077.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin-left: 15px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .group_3:active {
    transform: scale(0.95);
  }
  
  .label_2 {
    width: 32px;
    height: 29px;
    margin: 24px 0 0 25px;
  }
  
  .text_4 {
    width: 215px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 15px 49px 0 24px;
  }
  
  .group_4 {
    position: absolute;
    left: 1468px;
    top: 1px;
    width: 227px;
    height: 91px;
    background: url(../assets/images/treatmentprocess/远端治疗关闭灰色.png)
      100% no-repeat;
    background-size: 100% 100%;
    cursor: not-allowed;
  }

  .disabled-button {
    cursor: not-allowed !important;
    /* 移除opacity和额外的背景图片，因为新图片已经是灰色的 */
  }

  .disabled-button:active {
    transform: none !important;
  }

  /* 隐藏内部元素，因为背景图片已包含所有内容 */
  .hidden {
    display: none !important;
  }
  
  .label_3 {
    width: 37px;
    height: 37px;
    margin: 20px 0 0 33px;
  }
  
  .text_6 {
    width: 70px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 15px 62px 0 25px;
  }
  
  .block_3 {
    width: 1699px;
    height: 279px;
    margin: 142px 0 400px 105px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    gap: 105px;
    overflow: hidden;
  }
  
  .treatment-card {
    box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
    background-color: rgba(255, 255, 255, 1);
    width: 496px;
    height: 278px;
    border: 0.5px solid rgba(254, 152, 8, 1);
    flex-shrink: 0;
  }
  
  .card-1 {
    margin-top: 1px;
  }
  
  .card-2 {
    /* 通过 gap 控制间距 */
  }
  
  .card-3 {
    /* 通过 gap 控制间距 */
  }
  
  .card-header {
    width: 496px;
    height: 106px;
    background: url(../assets/images/treatmentprocess/8b04f3885006c864b55e5ac79f51715d.png)
      100% no-repeat;
    background-size: 100% 100%;
  }
  
  .image-text {
    width: 128px;
    height: 34px;
    margin: 30px 0 0 40px;
  }
  
  .label {
    margin-top: 7%;
    width: 34px;
    height: 34px;
  }
  
  .text-group {
    width: 80px;
    height: 26px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 25px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 4px;
  }
  
  .time-row {
    width: 381px;
    height: 33px;
    margin: 46px 0 0 57px;
  }
  
  .text_7 {
    width: 140px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_8 {
    width: 159px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .divider {
    width: 408px;
    height: 3px;
    margin: 20px 0 0 48px;
  }
  
  .intensity-row {
    width: 411px;
    height: 33px;
    margin: 13px 0 33px 57px;
  }
  
  .text_9 {
    width: 142px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_10 {
    width: 220px;
    height: 31px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 2px;
  }
  .text_11 {
    width: 140px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_12 {
    width: 159px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  .text_13 {
    width: 142px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_14 {
    width: 220px;
    height: 31px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 2px;
  }
  .text_15 {
    width: 140px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_16 {
    width: 159px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  .text_17 {
    width: 142px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_18 {
    width: 220px;
    height: 31px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 2px;
  }
  
  .button {
    transition: transform 0.1s ease;
  }
  
  .button:active {
    transform: scale(0.95);
  }
  
  .flex-row {
    display: flex;
    flex-direction: row;
  }
  
  .flex-col {
    display: flex;
    flex-direction: column;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  </style>


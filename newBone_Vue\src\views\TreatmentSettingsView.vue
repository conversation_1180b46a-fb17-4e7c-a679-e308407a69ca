<template>
  <div class="page">
    <div class="box_1">
      <div class="image-wrapper_1">
        <img
          class="image_1"
          :src="backButtonImg"
          @click="goBack"
          alt="返回"
        />
        <div class="title_text">治疗参数设置</div>
      </div>
      <div class="section_1">
        <div class="image-wrapper_2">
          <div class="body-image-stack">
            <!-- 底层正面人体图 -->
            <img
              class="base-body-image image_3"
              :src="frontBodyImg"
              alt="正面人体"
            />
            <!-- 正面标识文字 -->
            <div class="body-label front-label">正面</div>
            <!-- 基础轮廓和部位图片 -->
            <img
              v-for="(image, index) in getLeftBodyImages()"
              :key="index"
              class="image_3"
              :class="{ 'stacked-image': index > 0 }"
              :src="image"
              :alt="`人体图-左-${index}`"
            />
            <!-- 颜色图标 - 绝对定位 -->
            <div
              v-for="(icon, index) in getLeftColorIcons()"
              :key="`icon-${index}`"
              class="color-icon"
              :style="{ left: icon.x + 'px', top: icon.y + 'px' }"
            >
              <img :src="icon.image" :alt="`颜色图标-${index}`" />
    </div>
          </div>
        </div>
        <div class="section_2">
          <div class="image-wrapper_3">
            <!-- 按钮已移动到页面最外层进行调试 -->
          </div>
          <div class="group_1">
            <!-- 表头行 -->
            <div class="table-header">
              <div class="header-cell header-select">状态</div>
              <div class="header-cell header-part">部位</div>
              <div class="header-cell header-time">时间</div>
              <div class="header-cell header-intensity">强度</div>
              <div class="header-cell header-frequency">脉冲频率</div>
              <div class="header-cell header-patch">治疗贴片</div>
            </div>
            <!-- 肩颈部行 -->
            <div class="image-wrapper_4">
              <img 
                class="image_6" 
                :src="getStateImage('shoulderNeck')" 
                @click="selectPart('shoulderNeck')"
                alt="状态"
              />
              <div class="param-text_7">肩颈部</div>
              <div class="time-selector_8">
                <div 
                  class="time-display"
                  @click="toggleTimeWheel('shoulderNeck')"
                >
                  {{ treatmentParts.shoulderNeck.time }}
                </div>
                                  <div 
                    v-if="showTimeWheel.shoulderNeck"
                    class="time-picker-popup"
                  >
                    <div 
                      class="time-picker-container"
                      @mousedown="startDrag('shoulderNeck', $event)"
                      @touchstart="startDrag('shoulderNeck', $event)"
                    >
                    <div 
                      v-for="(timeOption, index) in getFiveTimeOptions('shoulderNeck')" 
                      :key="timeOption.value"
                      class="time-picker-item"
                      :class="{ 
                        'current': timeOption.type === 'current',
                        'near': timeOption.type === 'near',
                        'far': timeOption.type === 'far'
                      }"
                      @click="selectTimeFromDropdown('shoulderNeck', timeOption.value)"
                    >
                      {{ timeOption.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="intensity-selector_9">
                <div 
                  class="intensity-display"
                  @click="toggleDropdown('intensity', 'shoulderNeck')"
                >
                  {{ treatmentParts.shoulderNeck.intensity }}mW/cm²
                </div>
                <div 
                  v-if="showIntensityDropdown.shoulderNeck"
                  class="dropdown-box_1"
                >
                  <div 
                    v-for="option in intensityOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.shoulderNeck.intensity === option.value }"
                    @click="treatmentParts.shoulderNeck.intensity = option.value; showIntensityDropdown.shoulderNeck = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="frequency-selector_10">
                <div 
                  class="frequency-display"
                  @click="toggleDropdown('frequency', 'shoulderNeck')"
                >
                  {{ treatmentParts.shoulderNeck.frequency }}Hz
                </div>
                <div 
                  v-if="showFrequencyDropdown.shoulderNeck"
                  class="dropdown-group_6"
                >
                  <div 
                    v-for="option in frequencyOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.shoulderNeck.frequency === option.value }"
                    @click="treatmentParts.shoulderNeck.frequency = option.value; showFrequencyDropdown.shoulderNeck = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="treatment-patch-container_11">
                <div 
                  class="treatment-patch-clickable-area"
                  @click="toggleDropdown('treatmentPatch', 'shoulderNeck')"
                >
                  <div class="treatment-patch-display">
                    <span>{{ treatmentParts.shoulderNeck.depth }}</span>
                    <span>/{{ treatmentParts.shoulderNeck.count }}</span>
                  </div>
                </div>
                <div 
                  v-if="showTreatmentPatchDropdown.shoulderNeck"
                  class="dropdown-box_2"
                >
                  <div class="dropdown-header">
                    <span>深浅</span>
                    <span>数量</span>
                  </div>
                  <div class="dropdown-content-horizontal">
                    <div class="depth-section">

                                              <div class="depth-option" @click="selectDepth('shoulderNeck', '深部')">
                          <span class="checkbox-btn" :class="{ active: treatmentParts.shoulderNeck.depth === '深部' }"></span>
                          <span>深</span>
        </div>
                        <div class="depth-option" @click="selectDepth('shoulderNeck', '浅部')">
                          <span class="checkbox-btn" :class="{ active: treatmentParts.shoulderNeck.depth === '浅部' }"></span>
                          <span>浅</span>
                        </div>
                    </div>
                    <div class="count-section">
                      <div class="count-options-two-columns">
                        <!-- 第一列：1-4 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['1', '2', '3', '4']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('shoulderNeck', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.shoulderNeck.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                        <!-- 第二列：5-6 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['5', '6']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('shoulderNeck', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.shoulderNeck.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
          </div>
        </div>
      </div>

            <!-- 腰背部行 -->
            <div class="image-wrapper_5">
              <img 
                class="image_12" 
                :src="getStateImage('lowerBack')" 
                @click="selectPart('lowerBack')"
                alt="状态"
              />
              <div class="param-text_13">腰背部</div>
              <div class="time-selector_14">
                <div 
                  class="time-display"
                  @click="toggleTimeWheel('lowerBack')"
                >
                  {{ treatmentParts.lowerBack.time }}
            </div>
                <div 
                  v-if="showTimeWheel.lowerBack"
                  class="time-picker-popup"
                >
                  <div 
                    class="time-picker-container"
                    @mousedown="startDrag('lowerBack', $event)"
                    @touchstart="startDrag('lowerBack', $event)"
                  >
                    <div 
                      v-for="(timeOption, index) in getFiveTimeOptions('lowerBack')" 
                      :key="timeOption.value"
                      class="time-picker-item"
                      :class="{ 
                        'current': timeOption.type === 'current',
                        'near': timeOption.type === 'near',
                        'far': timeOption.type === 'far'
                      }"
                      @click="selectTimeFromDropdown('lowerBack', timeOption.value)"
                    >
                      {{ timeOption.label }}
          </div>
                  </div>
                </div>
              </div>
              <div class="intensity-selector_15">
                <div 
                  class="intensity-display"
                  @click="toggleDropdown('intensity', 'lowerBack')"
                >
                  {{ treatmentParts.lowerBack.intensity }}mW/cm²
                </div>
                <div 
                  v-if="showIntensityDropdown.lowerBack"
                  class="dropdown-box_1"
                >
                  <div 
                    v-for="option in intensityOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.lowerBack.intensity === option.value }"
                    @click="treatmentParts.lowerBack.intensity = option.value; showIntensityDropdown.lowerBack = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="frequency-selector_16">
                <div 
                  class="frequency-display"
                  @click="toggleDropdown('frequency', 'lowerBack')"
                >
                  {{ treatmentParts.lowerBack.frequency }}Hz
                </div>
                <div 
                  v-if="showFrequencyDropdown.lowerBack"
                  class="dropdown-group_6"
                >
                  <div 
                    v-for="option in frequencyOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.lowerBack.frequency === option.value }"
                    @click="treatmentParts.lowerBack.frequency = option.value; showFrequencyDropdown.lowerBack = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="treatment-patch-container_17">
                <div 
                  class="treatment-patch-clickable-area"
                  @click="toggleDropdown('treatmentPatch', 'lowerBack')"
                >
                  <div class="treatment-patch-display">
                    <span>{{ treatmentParts.lowerBack.depth }}</span>
                    <span>/{{ treatmentParts.lowerBack.count }}</span>
                  </div>
                </div>
                <div 
                  v-if="showTreatmentPatchDropdown.lowerBack"
                  class="dropdown-box_2"
                >
                  <div class="dropdown-header">
                    <span>深浅</span>
                    <span>数量</span>
                  </div>
                  <div class="dropdown-content-horizontal">
                    <div class="depth-section">

                                              <div class="depth-option" @click="selectDepth('lowerBack', '深部')">
                          <span class="checkbox-btn" :class="{ active: treatmentParts.lowerBack.depth === '深部' }"></span>
                          <span>深</span>
                        </div>
                        <div class="depth-option" @click="selectDepth('lowerBack', '浅部')">
                          <span class="checkbox-btn" :class="{ active: treatmentParts.lowerBack.depth === '浅部' }"></span>
                          <span>浅</span>
                        </div>
                    </div>
                    <div class="count-section">
                      <div class="count-options-two-columns">
                        <!-- 第一列：1-4 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['1', '2', '3', '4']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('lowerBack', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.lowerBack.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                        <!-- 第二列：5-6 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['5', '6']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('lowerBack', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.lowerBack.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 髋部行 -->
            <div class="image-wrapper_6">
              <img 
                class="image_18" 
                :src="getStateImage('hip')" 
                @click="selectPart('hip')"
                alt="状态"
              />
              <div class="param-text_19">髋部</div>
              <div class="time-selector_20">
                <div 
                  class="time-display"
                  @click="toggleTimeWheel('hip')"
                >
                  {{ treatmentParts.hip.time }}
                </div>
                <div 
                  v-if="showTimeWheel.hip"
                  class="time-picker-popup"
                >
                  <div 
                    class="time-picker-container"
                    @mousedown="startDrag('hip', $event)"
                    @touchstart="startDrag('hip', $event)"
                  >
                    <div 
                      v-for="(timeOption, index) in getFiveTimeOptions('hip')" 
                      :key="timeOption.value"
                      class="time-picker-item"
                      :class="{ 
                        'current': timeOption.type === 'current',
                        'near': timeOption.type === 'near',
                        'far': timeOption.type === 'far'
                      }"
                      @click="selectTimeFromDropdown('hip', timeOption.value)"
                    >
                      {{ timeOption.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="intensity-selector_21">
                <div 
                  class="intensity-display"
                  @click="toggleDropdown('intensity', 'hip')"
                >
                  {{ treatmentParts.hip.intensity }}mW/cm²
                </div>
                <div 
                  v-if="showIntensityDropdown.hip"
                  class="dropdown-box_1"
                >
                  <div 
                    v-for="option in intensityOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.hip.intensity === option.value }"
                    @click="treatmentParts.hip.intensity = option.value; showIntensityDropdown.hip = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="frequency-selector_22">
                <div 
                  class="frequency-display"
                  @click="toggleDropdown('frequency', 'hip')"
                >
                  {{ treatmentParts.hip.frequency }}Hz
                </div>
                <div 
                  v-if="showFrequencyDropdown.hip"
                  class="dropdown-group_6"
                >
                  <div 
                    v-for="option in frequencyOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.hip.frequency === option.value }"
                    @click="treatmentParts.hip.frequency = option.value; showFrequencyDropdown.hip = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="treatment-patch-container_23">
                <div 
                  class="treatment-patch-clickable-area"
                  @click="toggleDropdown('treatmentPatch', 'hip')"
                >
                  <div class="treatment-patch-display">
                    <span>{{ treatmentParts.hip.depth }}</span>
                    <span>/{{ treatmentParts.hip.count }}</span>
                  </div>
                </div>
                <div 
                  v-if="showTreatmentPatchDropdown.hip"
                  class="dropdown-box_2"
                >
                  <div class="dropdown-header">
                    <span>深浅</span>
                    <span>数量</span>
                  </div>
                  <div class="dropdown-content-horizontal">
                    <div class="depth-section">

                      <div class="depth-option" @click="selectDepth('hip', '深部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.hip.depth === '深部' }"></span>
                        <span>深</span>
                      </div>
                      <div class="depth-option" @click="selectDepth('hip', '浅部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.hip.depth === '浅部' }"></span>
                        <span>浅</span>
                      </div>
                    </div>
                    <div class="count-section">
                      <div class="count-options-two-columns">
                        <!-- 第一列：1-4 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['1', '2', '3', '4']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('hip', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.hip.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                        <!-- 第二列：5-6 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['5', '6']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('hip', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.hip.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 上肢行 -->
            <div class="image-wrapper_7">
              <img 
                class="image_24" 
                :src="getStateImage('upperLimb')" 
                @click="selectPart('upperLimb')"
                alt="状态"
              />
              <div class="param-text_25">上肢</div>
              <div class="time-selector_26">
                <div 
                  class="time-display"
                  @click="toggleTimeWheel('upperLimb')"
                >
                  {{ treatmentParts.upperLimb.time }}
                </div>
                <div 
                  v-if="showTimeWheel.upperLimb"
                  class="time-picker-popup"
                >
                  <div 
                    class="time-picker-container"
                    @mousedown="startDrag('upperLimb', $event)"
                    @touchstart="startDrag('upperLimb', $event)"
                  >
                    <div 
                      v-for="(timeOption, index) in getFiveTimeOptions('upperLimb')" 
                      :key="timeOption.value"
                      class="time-picker-item"
                      :class="{ 
                        'current': timeOption.type === 'current',
                        'near': timeOption.type === 'near',
                        'far': timeOption.type === 'far'
                      }"
                      @click="selectTimeFromDropdown('upperLimb', timeOption.value)"
                    >
                      {{ timeOption.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="intensity-selector_27">
                <div 
                  class="intensity-display"
                  @click="toggleDropdown('intensity', 'upperLimb')"
                >
                  {{ treatmentParts.upperLimb.intensity }}mW/cm²
                </div>
                <div 
                  v-if="showIntensityDropdown.upperLimb"
                  class="dropdown-box_1"
                >
                  <div 
                    v-for="option in intensityOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.upperLimb.intensity === option.value }"
                    @click="treatmentParts.upperLimb.intensity = option.value; showIntensityDropdown.upperLimb = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="frequency-selector_28">
                <div 
                  class="frequency-display"
                  @click="toggleDropdown('frequency', 'upperLimb')"
                >
                  {{ treatmentParts.upperLimb.frequency }}Hz
                </div>
                <div 
                  v-if="showFrequencyDropdown.upperLimb"
                  class="dropdown-group_6"
                >
                  <div 
                    v-for="option in frequencyOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.upperLimb.frequency === option.value }"
                    @click="treatmentParts.upperLimb.frequency = option.value; showFrequencyDropdown.upperLimb = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="treatment-patch-container_29">
                <div 
                  class="treatment-patch-clickable-area"
                  @click="toggleDropdown('treatmentPatch', 'upperLimb')"
                >
                  <div class="treatment-patch-display">
                    <span>{{ treatmentParts.upperLimb.depth }}</span>
                    <span>/{{ treatmentParts.upperLimb.count }}</span>
                  </div>
                </div>
                <div 
                  v-if="showTreatmentPatchDropdown.upperLimb"
                  class="dropdown-box_2"
                >
                  <div class="dropdown-header">
                    <span>深浅</span>
                    <span>数量</span>
                  </div>
                  <div class="dropdown-content-horizontal">
                    <div class="depth-section">

                      <div class="depth-option" @click="selectDepth('upperLimb', '深部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.upperLimb.depth === '深部' }"></span>
                        <span>深</span>
                      </div>
                      <div class="depth-option" @click="selectDepth('upperLimb', '浅部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.upperLimb.depth === '浅部' }"></span>
                        <span>浅</span>
                      </div>
                    </div>
                    <div class="count-section">
                      <div class="count-options-two-columns">
                        <!-- 第一列：1-4 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['1', '2', '3', '4']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('upperLimb', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.upperLimb.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                        <!-- 第二列：5-6 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['5', '6']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('upperLimb', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.upperLimb.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 下肢行 -->
            <div class="image-wrapper_8">
              <img 
                class="image_30" 
                :src="getStateImage('lowerLimb')" 
                @click="selectPart('lowerLimb')"
                alt="状态"
              />
              <div class="param-text_31">下肢</div>
              <div class="time-selector_32">
                <div 
                  class="time-display"
                  @click="toggleTimeWheel('lowerLimb')"
                >
                  {{ treatmentParts.lowerLimb.time }}
                </div>
                <div 
                  v-if="showTimeWheel.lowerLimb"
                  class="time-picker-popup"
                >
                  <div 
                    class="time-picker-container"
                    @mousedown="startDrag('lowerLimb', $event)"
                    @touchstart="startDrag('lowerLimb', $event)"
                  >
                    <div 
                      v-for="(timeOption, index) in getFiveTimeOptions('lowerLimb')" 
                      :key="timeOption.value"
                      class="time-picker-item"
                      :class="{ 
                        'current': timeOption.type === 'current',
                        'near': timeOption.type === 'near',
                        'far': timeOption.type === 'far'
                      }"
                      @click="selectTimeFromDropdown('lowerLimb', timeOption.value)"
                    >
                      {{ timeOption.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="intensity-selector_33">
                <div 
                  class="intensity-display"
                  @click="toggleDropdown('intensity', 'lowerLimb')"
                >
                  {{ treatmentParts.lowerLimb.intensity }}mW/cm²
                </div>
                <div 
                  v-if="showIntensityDropdown.lowerLimb"
                  class="dropdown-box_1"
                >
                  <div 
                    v-for="option in intensityOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.lowerLimb.intensity === option.value }"
                    @click="treatmentParts.lowerLimb.intensity = option.value; showIntensityDropdown.lowerLimb = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="frequency-selector_34">
                <div 
                  class="frequency-display"
                  @click="toggleDropdown('frequency', 'lowerLimb')"
                >
                  {{ treatmentParts.lowerLimb.frequency }}Hz
                </div>
                <div 
                  v-if="showFrequencyDropdown.lowerLimb"
                  class="dropdown-group_6"
                >
                  <div 
                    v-for="option in frequencyOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.lowerLimb.frequency === option.value }"
                    @click="treatmentParts.lowerLimb.frequency = option.value; showFrequencyDropdown.lowerLimb = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="treatment-patch-container_35">
                <div 
                  class="treatment-patch-clickable-area"
                  @click="toggleDropdown('treatmentPatch', 'lowerLimb')"
                >
                  <div class="treatment-patch-display">
                    <span>{{ treatmentParts.lowerLimb.depth }}</span>
                    <span>/{{ treatmentParts.lowerLimb.count }}</span>
                  </div>
                </div>
                <div 
                  v-if="showTreatmentPatchDropdown.lowerLimb"
                  class="dropdown-box_2"
                >
                  <div class="dropdown-header">
                    <span>深浅</span>
                    <span>数量</span>
                  </div>
                  <div class="dropdown-content-horizontal">
                    <div class="depth-section">

                      <div class="depth-option" @click="selectDepth('lowerLimb', '深部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.lowerLimb.depth === '深部' }"></span>
                        <span>深</span>
                      </div>
                      <div class="depth-option" @click="selectDepth('lowerLimb', '浅部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.lowerLimb.depth === '浅部' }"></span>
                        <span>浅</span>
                      </div>
                    </div>
                    <div class="count-section">
                      <div class="count-options-two-columns">
                        <!-- 第一列：1-4 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['1', '2', '3', '4']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('lowerLimb', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.lowerLimb.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                        <!-- 第二列：5-6 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['5', '6']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('lowerLimb', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.lowerLimb.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 其他部位行 -->
            <div class="image-wrapper_9">
              <img 
                class="image_36" 
                :src="getStateImage('other')" 
                @click="selectPart('other')"
                alt="状态"
              />
              <!-- 固定显示"其他部位"，不可编辑 -->
              <div class="param-text_37">
                其他部位
              </div>
              <div class="time-selector_38">
                <div 
                  class="time-display"
                  @click="toggleTimeWheel('other')"
                >
                  {{ treatmentParts.other.time }}
              </div>
                <div 
                  v-if="showTimeWheel.other"
                  class="time-picker-popup"
                >
                  <div 
                    class="time-picker-container"
                    @mousedown="startDrag('other', $event)"
                    @touchstart="startDrag('other', $event)"
                  >
                    <div 
                      v-for="(timeOption, index) in getFiveTimeOptions('other')" 
                      :key="timeOption.value"
                      class="time-picker-item"
                      :class="{ 
                        'current': timeOption.type === 'current',
                        'near': timeOption.type === 'near',
                        'far': timeOption.type === 'far'
                      }"
                      @click="selectTimeFromDropdown('other', timeOption.value)"
                    >
                      {{ timeOption.label }}
              </div>
              </div>
              </div>
              </div>
              <div class="intensity-selector_39">
                <div 
                  class="intensity-display"
                  @click="toggleDropdown('intensity', 'other')"
                >
                  {{ treatmentParts.other.intensity }}mW/cm²
            </div>
                <div 
                  v-if="showIntensityDropdown.other"
                  class="dropdown-box_1"
                >
                  <div 
                    v-for="option in intensityOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.other.intensity === option.value }"
                    @click="treatmentParts.other.intensity = option.value; showIntensityDropdown.other = false"
                  >
                    {{ option.label }}
          </div>
        </div>
              </div>
              <div class="frequency-selector_40">
                <div 
                  class="frequency-display"
                  @click="toggleDropdown('frequency', 'other')"
                >
                  {{ treatmentParts.other.frequency }}Hz
                </div>
                <div 
                  v-if="showFrequencyDropdown.other"
                  class="dropdown-group_6"
                >
                  <div 
                    v-for="option in frequencyOptions" 
                    :key="option.value"
                    class="dropdown-item"
                    :class="{ active: treatmentParts.other.frequency === option.value }"
                    @click="treatmentParts.other.frequency = option.value; showFrequencyDropdown.other = false"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
              <div class="treatment-patch-container_41">
                <div 
                  class="treatment-patch-clickable-area treatment-patch-clickable-area-wider"
                  @click="toggleDropdown('treatmentPatch', 'other')"
                >
                  <div class="treatment-patch-display treatment-patch-display-wider">
                    <span>{{ treatmentParts.other.depth }}</span>
                    <span>/{{ treatmentParts.other.count }}</span>
                  </div>
                </div>
                <div 
                  v-if="showTreatmentPatchDropdown.other"
                  class="dropdown-box_2"
                >
                  <div class="dropdown-header">
                    <span>深浅</span>
                    <span>数量</span>
                  </div>
                  <div class="dropdown-content-horizontal">
                    <div class="depth-section">

                      <div class="depth-option" @click="selectDepth('other', '深部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.other.depth === '深部' }"></span>
                        <span>深</span>
        </div>
                      <div class="depth-option" @click="selectDepth('other', '浅部')">
                        <span class="checkbox-btn" :class="{ active: treatmentParts.other.depth === '浅部' }"></span>
                        <span>浅</span>
      </div>
                    </div>
                    <div class="count-section">
                      <div class="count-options-two-columns">
                        <!-- 第一列：1-4 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['1', '2', '3', '4']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('other', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.other.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                        <!-- 第二列：5-6 -->
                        <div class="count-column">
                          <div 
                            v-for="count in ['5', '6']" 
                            :key="count"
                            class="count-option"
                            @click="selectCount('other', count)"
                          >
                            <span class="checkbox-btn" :class="{ active: treatmentParts.other.count === count }"></span>
                            <span>{{ count }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
      </div>
    </div>
              </div>
            </div>
          </div>
        </div>
        <div class="image-wrapper_10">
                      <div class="body-image-stack">
              <!-- 底层背面人体图 -->
              <img
                class="base-body-image image_42"
                :src="backBodyImg"
                alt="背面人体"
              />
              <!-- 背面标识文字 -->
              <div class="body-label back-label">背面</div>
              <!-- 基础轮廓和部位图片 -->
              <img
                v-for="(image, index) in getRightBodyImages()"
                :key="index"
                class="image_42"
                :class="{ 'stacked-image': index > 0 }"
                :src="image"
                :alt="`人体图-右-${index}`"
              />
              <!-- 颜色图标 - 绝对定位 -->
              <div
                v-for="(icon, index) in getRightColorIcons()"
                :key="`icon-${index}`"
                class="color-icon"
                :style="{ left: icon.x + 'px', top: icon.y + 'px' }"
              >
                <img :src="icon.image" :alt="`颜色图标-${index}`" />
              </div>
              </div>
            </div>
          </div>
        </div>

    <!-- 治疗头选择弹窗 -->
    <TreatmentHeadSelectionModal
      :visible="showTreatmentHeadModal"
      :recommendedHeads="recommendedTreatmentHeads"
      :treatmentMode="treatmentMode"
      @close="closeTreatmentHeadModal"
      @confirm="confirmTreatmentHeadSelection"
    />

    <!-- 治疗头数量不足弹窗 -->
    <TreatmentHeadShortageModal
      :visible="showTreatmentHeadShortageModal"
      @close="closeTreatmentHeadShortageModal"
    />

    <!-- 治疗头资源校验失败弹窗 -->
    <TreatmentHeadValidationModal
      :visible="showTreatmentHeadValidationModal"
      :message="validationErrorMessage"
      @close="closeTreatmentHeadValidationModal"
    />

    <!-- 参数下载中弹窗 -->
    <ParameterDownloadingModal
      :visible="showParameterDownloadingModal"
      @close="closeParameterDownloadingModal"
      @cancel="handleParameterDownloadCancel"
      @timeout="handleParameterDownloadTimeout"
    />

    <!-- 最终按钮 - 智能层级控制和点击动画 -->
    <div
      @click="downloadParameters"
      @mousedown="onButtonMouseDown($event)"
      @mouseup="onButtonMouseUp($event)"
      @mouseleave="onButtonMouseUp($event)"
      :style="{
        position: 'fixed',
        top: '933px',
        left: '1023px',
        width: '265px',
        height: '89px',
        zIndex: hasActiveModal ? 100 : 1000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        opacity: hasActiveModal ? 0.3 : 1,
        transition: 'all 0.2s ease',
        transform: 'scale(1)'
      }"
      class="treatment-button"
    >
      <img :src="downloadButtonImg" alt="远端治疗" style="width: 90%; height: 100%; object-fit: cover;" />
      <span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-family: MicrosoftYaHei; font-weight: normal; font-size: 33.33px; color: #595959; text-align: center; white-space: nowrap; pointer-events: none; z-index: 1;">
        远端治疗
      </span>
    </div>
    <div
      @click="startTreatment"
      @mousedown="onButtonMouseDown($event)"
      @mouseup="onButtonMouseUp($event)"
      @mouseleave="onButtonMouseUp($event)"
      :style="{
        position: 'fixed',
        top: '933px',
        left: '636px',
        width: '265px',
        height: '89px',
        zIndex: hasActiveModal ? 100 : 1000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        opacity: hasActiveModal ? 0.3 : 1,
        transition: 'all 0.2s ease',
        transform: 'scale(1)'
      }"
      class="treatment-button"
    >
      <img :src="playButtonImg" alt="本地治疗" style="width: 90%; height: 100%; object-fit: cover;" />
      <span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-family: MicrosoftYaHei; font-weight: normal; font-size: 33.33px; color: #595959; text-align: center; white-space: nowrap; pointer-events: none; z-index: 1;">
        本地治疗
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import http from '@/utils/axios';
import TreatmentHeadSelectionModal from '@/components/TreatmentHeadSelectionModal.vue';
import TreatmentHeadShortageModal from '@/components/TreatmentHeadShortageModal.vue';
import TreatmentHeadValidationModal from '@/components/TreatmentHeadValidationModal.vue';
import ParameterDownloadingModal from '@/components/ParameterDownloadingModal.vue';
import {
  checkTreatmentHeadAvailability,
  generateRecommendations,
  downloadTreatmentParameters,
  startLocalTreatment,
  confirmTreatmentHeadSelection as confirmTreatmentHeadSelectionAPI,
  startTreatmentProcess,
  type StartTreatmentRequest,
  type TreatmentDetailRequest,
  getRecords,
  createRecord,
  getPatientById
} from '@/api';
import {
  setTreatmentHeadLights,
  turnOffTreatmentHeadLights,
  turnOffAllLights
} from '@/api/hardware';

const route = useRoute();
const router = useRouter();
const patientId = route.params.patientId;

// 档案ID - 需要从患者ID获取当前的档案ID
const recordId = ref<number | null>(null);

// 导入所有图片资源
import backButtonImg from '@/assets/images/treatmentsettingsview/img/ps0rbopt6gpo79v6n1nus8nyhtdkihjzd4vl8a5a6d1c-9d09-4ddd-a32d-e212ee18a676.png';
import titleImg from '@/assets/images/treatmentsettingsview/img/psngud7iqb5drltc0gfaxen6217ul1g1k51a825a50-4801-462f-bc2b-f89aa0467986.png';
import downloadButtonImg from '@/assets/images/treatmentsettingsview/img/downloading.png';
import playButtonImg from '@/assets/images/treatmentsettingsview/img/downloading.png';

// 人体图
import defaultLeftBodyImg from '@/assets/images/treatmentsettingsview/img/pssqqmg9gb0xgo54f68gwr58fw7vzq3blm81bc9f08-377a-4c84-aae6-75c0eb8e9f8d.png';
import defaultRightBodyImg from '@/assets/images/treatmentsettingsview/img/psoaj5bukylfjjb2eu9jtfopouwhkbfqn12ef9cd4e-602b-4725-8a88-1327efa9db7d.png';

// 底层人体图片 - 正面和背面
import frontBodyImg from '@/assets/images/treatmentsettingsview/img/0.正.png';
import backBodyImg from '@/assets/images/treatmentsettingsview/img/0.背.png';
import shoulderLeftImg from '@/assets/images/treatmentsettingsview/img/肩颈部-左边.png';
import shoulderRightImg from '@/assets/images/treatmentsettingsview/img/肩颈部-右边.png';
import lowerBackLeftImg from '@/assets/images/treatmentsettingsview/img/腰背部-左边.png';
import lowerBackRightImg from '@/assets/images/treatmentsettingsview/img/腰背部-右边.png';
import hipLeftImg from '@/assets/images/treatmentsettingsview/img/髋部-左边.png';
import hipRightImg from '@/assets/images/treatmentsettingsview/img/髋部-右边.png';
import upperLimbLeftImg from '@/assets/images/treatmentsettingsview/img/上肢-左边.png';
import upperLimbRightImg from '@/assets/images/treatmentsettingsview/img/上肢-右边.png';
import lowerLimbLeftImg from '@/assets/images/treatmentsettingsview/img/下肢-左边.png';
import lowerLimbRightImg from '@/assets/images/treatmentsettingsview/img/下肢-右边.png';

// 新的状态图标 - 蓝色、绿色、橙色
import blueStateImg from '@/assets/images/交互界面/参数设置/参数设置/蓝色.png';
import greenStateImg from '@/assets/images/交互界面/参数设置/参数设置/绿色.png';
import orangeStateImg from '@/assets/images/交互界面/参数设置/参数设置/橙色.png';
import unselectedStateImg from '@/assets/images/treatmentsettingsview/img/ps0o1rfuciqnunhexw0dxpcj808nar1xuukac8a99a2-13ff-458f-a699-e73a9269b0be.png';

// 治疗头相关图片
import treatmentHeadSlot from '@/assets/images/treatmentsettingsview/img/治疗头卡槽.png';
import treatmentHeadConfirm from '@/assets/images/treatmentsettingsview/img/治疗头确定按钮.png';
import greenLightTreatmentHead from '@/assets/images/treatmentsettingsview/img/绿色灯治疗头.png';
import orangeLightTreatmentHead from '@/assets/images/treatmentsettingsview/img/橙色灯治疗头.png';
import blueLightTreatmentHead from '@/assets/images/treatmentsettingsview/img/蓝色灯治疗头.png';
import notInvolvedTreatmentHead from '@/assets/images/treatmentsettingsview/img/未涉及治疗头.png';

// 下拉框背景图片
import intensityDropdownBg from '@/assets/images/交互界面/参数设置/参数设置/强度下拉框.png';
import frequencyDropdownBg from '@/assets/images/交互界面/参数设置/参数设置/脉冲频率下拉框.png';
import treatmentPatchDropdownBg from '@/assets/images/交互界面/参数设置/参数设置/参数设置贴片数量.png';

// 人体轮廓基础图片
import bodyFrontOutline from '@/assets/images/交互界面/参数设置/参数设置/人体/0.正.png';
import bodyBackOutline from '@/assets/images/交互界面/参数设置/参数设置/人体/0.背.png';

// 人体部位图片 - 原始#F5E4D1颜色
import shoulderLeftOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a正肩颈2.png';
import shoulderRightOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a背肩颈1.png';
import lowerBackOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a腰背1.png';
import hipLeftOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a正髋部2.png';
import hipRightOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a背髋部1.png';
import upperLimbLeftOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a正上肢2.png';
import upperLimbRightOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a背上肢1.png';
import lowerLimbLeftOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a正下肢2.png';
import lowerLimbRightOriginal from '@/assets/images/交互界面/参数设置/参数设置/人体/a背下肢1.png';

const intensityOptions = [
  { label: '30mW/cm²', value: '30' },
  { label: '45mW/cm²', value: '45' },
  { label: '60mW/cm²', value: '60' }
];

const frequencyOptions = [
  { label: '100Hz', value: '100' },
  { label: '1000Hz', value: '1000' }
];

const depthOptions = [
  { label: '深部', value: '深部' },
  { label: '浅部', value: '浅部' }
];

const countOptions = [
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '4', value: '4' }
];

// 治疗部位数据
const treatmentParts = ref({
  shoulderNeck: {
    name: '肩颈部',
    selected: false,
    time: '20分钟',
    intensity: '30',
    frequency: '100',
    depth: '浅部',
    count: '2'
  },
  lowerBack: {
    name: '腰背部',
    selected: false,  // 修复：不应该默认选中
    time: '20分钟',
    intensity: '30',
    frequency: '100',
    depth: '深部',
    count: '2'
  },
  hip: {
    name: '髋部',
    selected: false,
    time: '20分钟',
    intensity: '30',
    frequency: '100',
    depth: '深部',
    count: '2'
  },
  upperLimb: {
    name: '上肢',
    selected: false,
    time: '20分钟',
    intensity: '30',
    frequency: '100',
    depth: '浅部',
    count: '2'
  },
  lowerLimb: {
    name: '下肢',
    selected: false,
    time: '20分钟',
    intensity: '30',
    frequency: '100',
    depth: '浅部',
    count: '2'
  },
  other: {
    name: '其他部位',
    selected: false,
    time: '20分钟',
    intensity: '30',
    frequency: '100',
    depth: '浅部',
    count: '2',
    customName: '其他部位'
  }
});

// 其他部位输入框的ref
const otherPartInput = ref<HTMLInputElement | null>(null);
// 是否正在编辑其他部位
const isEditingOtherPart = ref(false);

// 控制时间网格显示状态
const showTimeGrid = ref({
  shoulderNeck: false,
  lowerBack: false,
  hip: false,
  upperLimb: false,
  lowerLimb: false,
  other: false
});

// 控制下拉框显示状态
const showIntensityDropdown = ref({
  shoulderNeck: false,
  lowerBack: false,
  hip: false,
  upperLimb: false,
  lowerLimb: false,
  other: false
});

const showFrequencyDropdown = ref({
  shoulderNeck: false,
  lowerBack: false,
  hip: false,
  upperLimb: false,
  lowerLimb: false,
  other: false
});

const showTreatmentPatchDropdown = ref({
  shoulderNeck: false,
  lowerBack: false,
  hip: false,
  upperLimb: false,
  lowerLimb: false,
  other: false
});

// 治疗头选择弹窗状态
const showTreatmentHeadModal = ref(false);
const recommendedTreatmentHeads = ref<any[]>([]);

// 新增弹窗状态
const showTreatmentHeadShortageModal = ref(false);
const showTreatmentHeadValidationModal = ref(false);
const validationErrorMessage = ref('');
const showParameterDownloadingModal = ref(false);

// 正在处理确认请求（防重复点击）
const isConfirming = ref(false);

// 治疗模式状态
const treatmentMode = ref<'local' | 'takeaway'>('local');

// 检查是否有活动弹窗
const hasActiveModal = computed(() => {
  return showTreatmentHeadModal.value ||
         showTreatmentHeadShortageModal.value ||
         showTreatmentHeadValidationModal.value ||
         showParameterDownloadingModal.value;
});

// 按钮点击动画
const onButtonMouseDown = (event: MouseEvent) => {
  const target = event.currentTarget as HTMLElement;
  target.style.transform = 'scale(0.95)';
};

const onButtonMouseUp = (event: MouseEvent) => {
  const target = event.currentTarget as HTMLElement;
  target.style.transform = 'scale(1)';
};

// 选择顺序跟踪
const selectionOrder = ref<string[]>([]);

// 获取状态图标 - 按选择顺序分配颜色
const getStateImage = (partKey: string) => {
  const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
  if (!part.selected) {
    return unselectedStateImg;
  }
  
  // 获取该部位在选择顺序中的位置
  const orderIndex = selectionOrder.value.indexOf(partKey);
  if (orderIndex === -1) return unselectedStateImg;
  
  // 按选择顺序分配颜色：第一个蓝色，第二个绿色，第三个橙色
  const colorImages = [blueStateImg, greenStateImg, orangeStateImg];
  return colorImages[orderIndex] || blueStateImg;
};

// 获取部位对应的颜色代码
const getPartColor = (partKey: string) => {
  const orderIndex = selectionOrder.value.indexOf(partKey);
  if (orderIndex === -1) return null;
  
  const colors = ['#53B5D6', '#43CD77', '#FF9809']; // 蓝色、绿色、橙色
  return colors[orderIndex];
};

// 颜色转换函数 - 将#F5E4D1转换为目标颜色
const convertImageColor = (imageElement: HTMLImageElement, targetColor: string): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  
  canvas.width = imageElement.width;
  canvas.height = imageElement.height;
  
  ctx.drawImage(imageElement, 0, 0);
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  // 将#F5E4D1 (245, 228, 209) 替换为目标颜色
  const targetRGB = hexToRgb(targetColor);
  if (!targetRGB) return '';
  
  for (let i = 0; i < data.length; i += 4) {
    if (data[i] === 245 && data[i + 1] === 228 && data[i + 2] === 209) {
      data[i] = targetRGB.r;
      data[i + 1] = targetRGB.g;
      data[i + 2] = targetRGB.b;
    }
  }
  
  ctx.putImageData(imageData, 0, 0);
  return canvas.toDataURL();
};

// 十六进制颜色转RGB
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

// 滚轮时间选择器 - 类似iOS样式
const showTimeWheel = ref({
  shoulderNeck: false,
  lowerBack: false,
  hip: false,
  upperLimb: false,
  lowerLimb: false,
  other: false
});

// 时间选择器拖拽状态
const timeOffsets = ref({
  shoulderNeck: 0,
  lowerBack: 0,
  hip: 0,
  upperLimb: 0,
  lowerLimb: 0,
  other: 0
});

// 拖拽状态
const isDragging = ref(false);
const startY = ref(0);
const startOffset = ref(0);
const startTime = ref(15); // 拖拽开始时的时间值
const currentPartKey = ref('');
const velocity = ref(0);
const lastY = ref(0);
const lastTime = ref(0);

// 获取时间选项（当前时间前后各1分钟，总共3个选项）
const getTimeOptions = (currentTime: string) => {
  const current = parseInt(currentTime.replace('分钟', ''));
  const options = [];
  for (let i = Math.max(1, current - 1); i <= Math.min(30, current + 1); i++) {
    options.push(i);
  }
  // 确保总是有3个选项
  if (options.length < 3) {
    if (current === 1) {
      options.push(current + 2);
    } else if (current === 30) {
      options.unshift(current - 2);
    }
  }
  return options;
};

// 时间选择器 - 获取当前时间前后的5个时间（最上面30分钟，最下面1分钟）
const getFiveTimeOptions = (partKey: string) => {
  const current = getCurrentTimeValue(partKey);
  const options = [];
  
  // 前面2个时间
  const prev2 = Math.max(1, current - 2);
  const prev1 = Math.max(1, current - 1);
  
  // 后面2个时间
  const next1 = Math.min(30, current + 1);
  const next2 = Math.min(30, current + 2);
  
  options.push({ value: prev2, label: `${prev2}分钟`, type: 'far' });
  options.push({ value: prev1, label: `${prev1}分钟`, type: 'near' });
  options.push({ value: current, label: `${current}分钟`, type: 'current' });
  options.push({ value: next1, label: `${next1}分钟`, type: 'near' });
  options.push({ value: next2, label: `${next2}分钟`, type: 'far' });
  
  // 反转数组顺序，使最上面是30分钟，最下面是1分钟
  return options.reverse();
};

// 获取当前时间数值
const getCurrentTimeValue = (partKey: string) => {
  const timeStr = treatmentParts.value[partKey as keyof typeof treatmentParts.value].time;
  return parseInt(timeStr.replace('分钟', ''));
};

// 从下拉框选择时间
const selectTimeFromDropdown = (partKey: string, time: number) => {
  treatmentParts.value[partKey as keyof typeof treatmentParts.value].time = `${time}分钟`;
  showTimeWheel.value[partKey as keyof typeof showTimeWheel.value] = false;
};

// 开始拖拽
const startDrag = (partKey: string, event: MouseEvent | TouchEvent) => {
  event.preventDefault();
  isDragging.value = true;
  currentPartKey.value = partKey;
  
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;
  startY.value = clientY;
  lastY.value = clientY;
  startTime.value = getCurrentTimeValue(partKey); // 保存拖拽开始时的时间
  startOffset.value = timeOffsets.value[partKey as keyof typeof timeOffsets.value];
  lastTime.value = Date.now();
  velocity.value = 0;
  
  // 添加全局鼠标/触摸移动和释放事件
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', endDrag);
  document.addEventListener('touchmove', handleDrag);
  document.addEventListener('touchend', endDrag);
};

// 处理拖拽
const handleDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return;
  
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;
  const deltaY = clientY - startY.value;
  const currentTime = Date.now();
  const timeDelta = currentTime - lastTime.value;
  
  // 计算速度
  if (timeDelta > 0) {
    velocity.value = (clientY - lastY.value) / timeDelta;
  }
  
  // 更直接的移动对应：每30px对应1分钟的变化（减慢速度）
  const pixelsPerMinute = 30;
  const timeChange = Math.round(deltaY / pixelsPerMinute);
  
  // 计算新时间值（向下拖拽增加时间，向上拖拽减少时间）
  const newTimeValue = Math.max(1, Math.min(30, startTime.value + timeChange));
  
  // 更新时间
  treatmentParts.value[currentPartKey.value as keyof typeof treatmentParts.value].time = `${newTimeValue}分钟`;
  
  lastY.value = clientY;
  lastTime.value = currentTime;
};

// 结束拖拽
const endDrag = () => {
  if (!isDragging.value) return;
  
  isDragging.value = false;
  
  // 移除事件监听器
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', endDrag);
  document.removeEventListener('touchmove', handleDrag);
  document.removeEventListener('touchend', endDrag);
  
  // 惯性滚动
  if (Math.abs(velocity.value) > 0.1) {
    const pixelsPerMinute = 30;
    const inertiaChange = Math.round(velocity.value * 150 / pixelsPerMinute); // 惯性系数调整
    const currentTimeValue = getCurrentTimeValue(currentPartKey.value);
    const finalTimeValue = Math.max(1, Math.min(30, currentTimeValue + inertiaChange));
    
    // 平滑动画到最终时间
    animateTimeChange(currentPartKey.value, currentTimeValue, finalTimeValue);
  }
};

// 平滑动画时间变化
const animateTimeChange = (partKey: string, startTime: number, endTime: number) => {
  const duration = 300;
  const startAnimTime = Date.now();
  
  const animate = () => {
    const elapsed = Date.now() - startAnimTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // 使用easeOut函数
    const easeOutProgress = 1 - Math.pow(1 - progress, 3);
    
    const currentTime = Math.round(startTime + (endTime - startTime) * easeOutProgress);
    treatmentParts.value[partKey as keyof typeof treatmentParts.value].time = `${currentTime}分钟`;
    
    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };
  
  requestAnimationFrame(animate);
};

// 切换时间选择器显示
const toggleTimeWheel = (partKey: string) => {
  // 关闭其他部位的时间选择器
  Object.keys(showTimeWheel.value).forEach(key => {
    if (key !== partKey) {
      showTimeWheel.value[key as keyof typeof showTimeWheel.value] = false;
    }
  });
  showTimeWheel.value[partKey as keyof typeof showTimeWheel.value] = !showTimeWheel.value[partKey as keyof typeof showTimeWheel.value];
};

// 切换下拉框显示
const toggleDropdown = (type: string, partKey: string) => {
  const dropdownMap: { [key: string]: any } = {
    'intensity': showIntensityDropdown,
    'frequency': showFrequencyDropdown,
    'treatmentPatch': showTreatmentPatchDropdown
  };
  
  const targetDropdown = dropdownMap[type];
  if (targetDropdown) {
    // 关闭其他下拉框
    Object.keys(targetDropdown.value).forEach(key => {
      if (key !== partKey) {
        targetDropdown.value[key] = false;
      }
    });
    targetDropdown.value[partKey] = !targetDropdown.value[partKey];
  }
};

// 获取左侧人体图（基础轮廓+部位叠加）
const getLeftBodyImages = () => {
  const images = [bodyFrontOutline]; // 基础轮廓
  
  // 按选择顺序添加部位图片（不改颜色，其他部位不叠加）
  selectionOrder.value.forEach(partKey => {
    const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
    if (part.selected && partKey !== 'other') { // 其他部位不叠加部位图
      const imageMap: { [key: string]: string } = {
        'shoulderNeck': shoulderLeftOriginal,
        'hip': hipLeftOriginal,
        'upperLimb': upperLimbLeftOriginal,
        'lowerLimb': lowerLimbLeftOriginal
      };
      
      if (imageMap[partKey]) {
        images.push(imageMap[partKey]);
      }
    }
  });
  
  return images;
};

// 获取左侧颜色图标（绝对定位）
const getLeftColorIcons = () => {
  const icons: Array<{ x: number; y: number; image: string }> = [];
  
  selectionOrder.value.forEach((partKey, index) => {
    const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
    if (part.selected && partKey !== 'other') { // 其他部位不标注
      const colorIcons = [blueStateImg, greenStateImg, orangeStateImg];
      const positions: { [key: string]: { x: number; y: number } } = {
        'shoulderNeck': { x: 123, y: 126 }, // 颈部
        'hip': { x: 88, y: 328 }, // 髋部
        'upperLimb': { x: 197, y: 290 }, // 正面上肢
        'lowerLimb': { x: 92, y: 507 } // 正面下肢
      };
      
      if (colorIcons[index] && positions[partKey]) {
        icons.push({
          x: positions[partKey].x,
          y: positions[partKey].y,
          image: colorIcons[index]
        });
      }
    }
  });
  
  return icons;
};

// 获取右侧人体图（基础轮廓+部位叠加）
const getRightBodyImages = () => {
  const images = [bodyBackOutline]; // 基础轮廓
  
  // 按选择顺序添加部位图片（不改颜色，其他部位不叠加）
  selectionOrder.value.forEach(partKey => {
    const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
    if (part.selected && partKey !== 'other') { // 其他部位不叠加部位图
      const imageMap: { [key: string]: string } = {
        'shoulderNeck': shoulderRightOriginal,
        'lowerBack': lowerBackOriginal,
        'hip': hipRightOriginal,
        'upperLimb': upperLimbRightOriginal,
        'lowerLimb': lowerLimbRightOriginal
      };
      
      if (imageMap[partKey]) {
        images.push(imageMap[partKey]);
      }
    }
  });
  
  return images;
};

// 获取右侧颜色图标（绝对定位）
const getRightColorIcons = () => {
  const icons: Array<{ x: number; y: number; image: string }> = [];
  
  selectionOrder.value.forEach((partKey, index) => {
    const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
    if (part.selected && partKey !== 'other') { // 其他部位不标注
      const colorIcons = [blueStateImg, greenStateImg, orangeStateImg];
      const positions: { [key: string]: { x: number; y: number } } = {
        'shoulderNeck': { x: 285, y: 126 }, // 颈部（背面与正面相同）
        'lowerBack': { x: 285, y:221 }, // 腰背
        'hip': { x: 318, y: 328 }, // 髋部（背面与正面相同）
        'upperLimb': { x: 214, y: 290 }, // 背面上肢
        'lowerLimb': { x: 317, y: 507 } // 背面下肢
      };
      
      if (colorIcons[index] && positions[partKey]) {
        icons.push({
          x: positions[partKey].x,
          y: positions[partKey].y,
          image: colorIcons[index]
        });
      }
    }
  });
  
  return icons;
};

// 选择治疗部位 - 支持多选，最多3个，更新选择顺序
const selectPart = (partKey: string) => {
  const currentPart = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
  
  if (currentPart.selected) {
    // 如果已选中，则取消选择
    currentPart.selected = false;
    // 从选择顺序中移除
    const index = selectionOrder.value.indexOf(partKey);
    if (index > -1) {
      selectionOrder.value.splice(index, 1);
    }
  } else {
    // 如果未选中，检查是否已达到最大选择数量
    // 使用selectionOrder数组长度来确保准确性
    if (selectionOrder.value.length >= 3) {
    MessagePlugin.warning('最多只能选择3个治疗部位');
    return;
  }
    currentPart.selected = true;
    // 添加到选择顺序
    selectionOrder.value.push(partKey);
  }
};

// 切换时间网格显示状态
const toggleTimeGrid = (partKey: string) => {
  // 关闭其他部位的时间网格
  Object.keys(showTimeGrid.value).forEach(key => {
    if (key !== partKey) {
      showTimeGrid.value[key as keyof typeof showTimeGrid.value] = false;
    }
  });
  // 切换当前部位的时间网格
  showTimeGrid.value[partKey as keyof typeof showTimeGrid.value] = !showTimeGrid.value[partKey as keyof typeof showTimeGrid.value];
};

// 选择时间
const selectTime = (partKey: string, time: string) => {
  treatmentParts.value[partKey as keyof typeof treatmentParts.value].time = time;
  // 选择后关闭时间滚轮
  showTimeWheel.value[partKey as keyof typeof showTimeWheel.value] = false;
};
  
// 开始编辑其他部位
const startEditOtherPart = () => {
  isEditingOtherPart.value = true;
  // 使用nextTick确保DOM更新后再聚焦
  nextTick(() => {
    if (otherPartInput.value) {
      otherPartInput.value.focus();
    }
  });
};

// 停止编辑其他部位
const stopEditOtherPart = () => {
  isEditingOtherPart.value = false;
};

// 模拟后端接口：检查治疗头数量是否充足
const checkTreatmentHeadAvailabilityAPI = async (treatmentParams: any) => {
  try {
    const response = await checkTreatmentHeadAvailability({
      patientId: patientId as string,
      treatmentMode: treatmentParams.treatmentMode,
      bodyParts: treatmentParams.bodyParts
    });
    
    if (response.data.code === 200) {
      const data = response.data.data;
      return {
        sufficient: data.sufficient,
        needed: data.totalNeeded,
        available: data.totalAvailable,
        shallowNeeded: data.shallowNeeded,
        shallowAvailable: data.shallowAvailable,
        deepNeeded: data.deepNeeded,
        deepAvailable: data.deepAvailable,
        details: {
          shallow: { 
            needed: data.shallowNeeded, 
            available: data.shallowAvailable, 
            sufficient: data.shallowSufficient 
          },
          deep: { 
            needed: data.deepNeeded, 
            available: data.deepAvailable, 
            sufficient: data.deepSufficient 
          }
        }
      };
    } else {
      throw new Error(response.data.message || '检查治疗头数量失败');
    }
  } catch (error: any) {
    console.error('API调用失败:', error);
    throw new Error(error.message || '网络请求失败');
  }
};

// 模拟后端接口：根据参数设置生成推荐治疗头
const generateRecommendedTreatmentHeadsAPI = async (treatmentParams: any) => {
  try {
    const response = await generateRecommendations({
      patientId: patientId as string,
      treatmentMode: treatmentParams.treatmentMode || 'local',
      bodyParts: treatmentParams.bodyParts
    });

    // Axios拦截器已经提取了data.data到response.data
    // 所以直接使用response.data即可
    const recommendations = response.data.map((rec: any) => ({
      slotId: rec.slotNumber,
      row: Math.floor((rec.slotNumber - 1) / 2) + 1,
      position: ((rec.slotNumber - 1) % 2) + 1,
      color: rec.lightColorName,
      partName: rec.targetBodyPart,
      status: 'recommended',
      depth: rec.compartmentType === 'DEEP' ? '深部' : '浅部',
      intensity: rec.intensity,
      frequency: rec.frequency,
      time: `${rec.durationMinutes}分钟`,
      count: 1,
      headNumber: rec.slotNumber, // 添加治疗头编号
      colorCode: rec.lightColorCode || (rec.lightColorName === 'green' ? 3 : rec.lightColorName === 'orange' ? 1 : 2) // 添加颜色代码
    }));

    // 🚨 硬件集成：点亮推荐治疗头指示灯（TWSC指令）
    try {
      const lightRequests = recommendations.map(rec => ({
        headNumber: rec.headNumber,
        colorCode: rec.colorCode
      }));

      console.log('发送TWSC指令点亮推荐治疗头指示灯:', lightRequests);
      await setTreatmentHeadLights(lightRequests);
      console.log('推荐治疗头指示灯点亮成功');
      MessagePlugin.success('推荐治疗头指示灯已点亮');
    } catch (lightError: any) {
      console.error('点亮推荐治疗头指示灯失败:', lightError);
      MessagePlugin.warning('推荐治疗头指示灯控制失败，但推荐功能正常');
    }

    return recommendations;
  } catch (error: any) {
    console.error('API调用失败:', error);
    throw new Error(error.message || '网络请求失败');
  }
};

// 获取治疗头图片路径
const getTreatmentHeadImage = (row: number, position: number) => {
  const recommendation = recommendedTreatmentHeads.value.find(
    rec => rec.row === row && rec.position === position
  );
  
  if (recommendation) {
    // 根据颜色返回对应的治疗头图片
    switch (recommendation.color) {
      case 'green':
        return greenLightTreatmentHead;
      case 'orange':
        return orangeLightTreatmentHead;
      case 'blue':
        return blueLightTreatmentHead;
      default:
        return treatmentHeadSlot;
    }
  } else {
    // 检查是否有现有治疗头（模拟数据）
    const rowIndex = row - 1;
    const positionIndex = position - 1;
    const slotNumber = rowIndex * 2 + positionIndex + 1;
    
    // 模拟：某些位置有现有治疗头（未涉及的治疗头）
    const existingSlots = [3, 7, 11, 15]; // 模拟这些槽位有现有治疗头
    const hasExisting = existingSlots.includes(slotNumber);
    
    if (hasExisting) {
      return notInvolvedTreatmentHead;
    } else {
      return treatmentHeadSlot;
    }
  }
};



// 获取治疗头提示信息
const getTreatmentHeadTooltip = (row: number, position: number) => {
  const recommendation = recommendedTreatmentHeads.value.find(
    rec => rec.row === row && rec.position === position
  );
  
  if (recommendation) {
    return `推荐：${recommendation.partName} | ${recommendation.depth} | ${recommendation.intensity}mW/cm² | ${recommendation.frequency}Hz | ${recommendation.time}分钟`;
  } else {
    const rowIndex = row - 1;
    const positionIndex = position - 1;
    const slotNumber = rowIndex * 2 + positionIndex + 1;
    const existingSlots = [3, 7, 11, 15];
    const hasExisting = existingSlots.includes(slotNumber);
    
    if (hasExisting) {
      return '现有治疗头（未涉及本次治疗）';
    } else {
      return '空卡槽';
    }
  }
};

// 下载治疗参数 - 实现新的启动逻辑
const downloadParameters = async () => {
  const selectedParts = Object.values(treatmentParts.value).filter(part => part.selected);
  
  if (selectedParts.length === 0) {
    MessagePlugin.warning('请至少选择一个治疗部位');
    return;
  }

  try {
    // 设置为取走治疗模式（下载治疗参数）
    treatmentMode.value = 'takeaway';
    
    // 获取部位对应的颜色（按选择顺序分配）
    const getPartColorInfo = () => {
      const colors = ['blue', 'green', 'orange']; // 按选择顺序分配颜色
      const colorMapping: { [key: string]: string } = {};
      
      selectionOrder.value.forEach((partKey, index) => {
        const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
        if (part.selected) {
          colorMapping[part.name] = colors[index] || 'blue';
        }
      });
      
      return colorMapping;
    };

    const colorMapping = getPartColorInfo();
    
    // 发送参数到后端，检查治疗头数量
    const treatmentParams = {
      patientId: patientId,
      treatmentMode: 'takeaway', // 取走治疗模式
      bodyParts: selectedParts.map(part => ({
        name: (part as any).customName || part.name,
        color: colorMapping[part.name] || 'blue', // 添加颜色信息用于控制硬件灯光
        parameters: {
          time: part.time,
          intensity: `${part.intensity}mW/cm²`,
          frequency: `${part.frequency}Hz`,
          depth: part.depth,
          count: part.count
        }
      }))
    };

    console.log('发送取走治疗参数到后端:', treatmentParams);
    
    // 检查治疗头数量
    const response = await checkTreatmentHeadAvailability({
      patientId: patientId as string,
      treatmentMode: treatmentParams.treatmentMode as 'local' | 'takeaway',
      bodyParts: treatmentParams.bodyParts.map((part: any) => ({
        name: part.name,
        color: part.color,
        parameters: {
          time: part.parameters.time,
          intensity: part.parameters.intensity,
          frequency: part.parameters.frequency,
          depth: part.parameters.depth,
          count: parseInt(part.parameters.count)
        }
      }))
    });
    
    // axios拦截器已经提取了data字段，直接使用response.data
    const availabilityData = response.data;
    if (!availabilityData.sufficient) {
      // 治疗头数量不足
      showTreatmentHeadShortageModal.value = true;
    } else {
      // 治疗头数量充足，显示参数下载中弹窗
      showParameterDownloadingModal.value = true;
    }
  } catch (error: any) {
    console.error('检查取走治疗头数量失败:', error);

    // 检查是否是409错误（治疗头资源校验失败）
    if (error.response && error.response.status === 409) {
      validationErrorMessage.value = error.response.data?.message || '治疗头资源不足，请检查设备状态';
      showTreatmentHeadValidationModal.value = true;
    } else {
      MessagePlugin.error('检查取走治疗头数量失败，请稍后重试');
    }
  }
};

// 关闭治疗头选择弹窗
const closeTreatmentHeadModal = async () => {
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('用户取消选择，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
      // 不影响关闭弹窗，只记录错误
    }
  }

  showTreatmentHeadModal.value = false;
};

// 关闭治疗头数量不足弹窗
const closeTreatmentHeadShortageModal = async () => {
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('治疗头数量不足弹窗关闭，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
      // 不影响关闭弹窗，只记录错误
    }
  }

  showTreatmentHeadShortageModal.value = false;
};

// 关闭治疗头资源校验失败弹窗
const closeTreatmentHeadValidationModal = () => {
  showTreatmentHeadValidationModal.value = false;
  validationErrorMessage.value = '';
};

// 关闭参数下载中弹窗
const closeParameterDownloadingModal = async () => {
  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('参数下载弹窗关闭，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
      // 不影响关闭弹窗，只记录错误
    }
  }

  showParameterDownloadingModal.value = false;
};

// 处理参数下载弹窗的取消事件（用户手动关闭）
const handleParameterDownloadCancel = async () => {
  console.log('用户取消了参数下载');

  // 🚨 硬件集成：关闭推荐治疗头指示灯（TWSN指令）
  if (recommendedTreatmentHeads.value.length > 0) {
    try {
      const headNumbers = recommendedTreatmentHeads.value.map(head => head.headNumber);
      console.log('参数下载取消，发送TWSN指令关闭推荐治疗头指示灯:', headNumbers);
      await turnOffTreatmentHeadLights(headNumbers);
      console.log('推荐治疗头指示灯关闭成功');
    } catch (lightError: any) {
      console.error('关闭推荐治疗头指示灯失败:', lightError);
      // 不影响关闭弹窗，只记录错误
    }
  }

  // 发送取消信号到后端
  // 这里可以调用后端API取消启动
  MessagePlugin.info('已取消参数下载');
  closeParameterDownloadingModal();
};

// 处理参数下载弹窗的超时事件（3秒自动关闭）
const handleParameterDownloadTimeout = async () => {
  console.log('参数下载完成，显示治疗头选择弹窗');
  closeParameterDownloadingModal();
  
  try {
    // 获取部位对应的颜色（按选择顺序分配）
    const getPartColorInfo = () => {
      const colors = ['blue', 'green', 'orange']; // 按选择顺序分配颜色
      const colorMapping: { [key: string]: string } = {};
      
      selectionOrder.value.forEach((partKey, index) => {
        const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
        if (part.selected) {
          colorMapping[part.name] = colors[index] || 'blue';
        }
      });
      
      return colorMapping;
    };

    const colorMapping = getPartColorInfo();
    
    // 构建治疗参数数据
    const selectedParts = Object.values(treatmentParts.value).filter(part => part.selected);
    const treatmentParams = {
      patientId: patientId as string,
      treatmentMode: 'takeaway' as 'local' | 'takeaway',
      bodyParts: selectedParts.map(part => ({
        name: part.name,
        color: colorMapping[part.name] || 'blue',
        parameters: {
          time: part.time,
          intensity: `${part.intensity}mW/cm²`,
          frequency: `${part.frequency}Hz`,
          depth: part.depth,
          count: parseInt(part.count)
        }
      }))
    };
    
    // 生成推荐治疗头数据并显示选择弹窗
    recommendedTreatmentHeads.value = await generateRecommendedTreatmentHeadsAPI(treatmentParams);
    console.log('推荐治疗头:', recommendedTreatmentHeads.value);
    showTreatmentHeadModal.value = true;
  } catch (error) {
    console.error('生成推荐治疗头失败:', error);
    MessagePlugin.error('生成推荐治疗头失败，请稍后重试');
  }
};

// 确认治疗头选择并发送参数
const confirmTreatmentHeadSelection = async () => {
  // 防重复点击
  if (isConfirming.value) {
    console.log('正在处理中，忽略重复点击');
    return;
  }
  
  isConfirming.value = true;
  
  const selectedParts = Object.values(treatmentParts.value).filter(part => part.selected);
  
  console.log('确认治疗头选择 - patientId:', patientId, 'recordId:', recordId.value);
  
  try {
    // 获取部位对应的颜色（按选择顺序分配）
    const getPartColorInfo = () => {
      const colors = ['blue', 'green', 'orange']; // 按选择顺序分配颜色
      const colorMapping: { [key: string]: string } = {};
      
      selectionOrder.value.forEach((partKey, index) => {
        const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
        if (part.selected) {
          colorMapping[part.name] = colors[index] || 'blue';
        }
      });
      
      return colorMapping;
    };

    const colorMapping = getPartColorInfo();
    
    // 检查recordId是否有效
    if (!recordId.value || recordId.value <= 0 || isNaN(recordId.value)) {
      console.error('无效的档案ID:', recordId.value, 'patientId:', patientId);
      
      // 尝试重新获取档案ID
      await fetchPatientRecordId();
      
      // 再次检查
      if (!recordId.value || recordId.value <= 0 || isNaN(recordId.value)) {
        MessagePlugin.error('档案信息获取失败，请检查患者ID是否正确');
        isConfirming.value = false; // 重置确认状态
        return;
      }
    }
    
    console.log('使用档案ID:', recordId.value);

    // 构建启动治疗进程的请求数据
    const startTreatmentRequest: StartTreatmentRequest = {
      recordId: recordId.value,
      treatmentMode: treatmentMode.value === 'local' ? 'ON_SITE' : 'TAKE_AWAY',
      treatmentDetails: recommendedTreatmentHeads.value.map((head) => {
        // 根据部位名称找到对应的治疗参数
        const selectedPart = selectedParts.find(part => part.name === head.partName);
        
        if (!selectedPart) {
          console.error('无法找到部位对应的治疗参数:', head.partName);
          throw new Error(`部位 ${head.partName} 的治疗参数缺失`);
        }
        
        return {
          bodyPart: head.partName,
          headNumber: head.slotId,
          duration: parseInt(selectedPart.time) || 15,
          intensity: parseInt(selectedPart.intensity) || 30,
          frequency: parseInt(selectedPart.frequency) || 1000,
          patchType: selectedPart.depth === '深部' ? 'DEEP' : 'SHALLOW',
          patchQuantity: parseInt(selectedPart.count) || 1
        } as TreatmentDetailRequest;
      })
    };

    console.log('启动治疗进程请求:', startTreatmentRequest);
    
    try {
      // 启动治疗进程
      const response = await startTreatmentProcess(startTreatmentRequest);
      const processId = response.data.processId;
      
      console.log('治疗进程启动成功:', response.data);
      MessagePlugin.success(`治疗进程启动成功，进程ID: ${processId}`);
      showTreatmentHeadModal.value = false;
      isConfirming.value = false; // 重置确认状态
      
      // 根据治疗模式决定下一步，传递processId
      if (treatmentMode.value === 'local') {
        // 本地治疗，跳转到本地治疗进程页面
        router.push(`/treatment/process1/${processId}`);
      } else {
        // 取走治疗，跳转到取走治疗页面
        router.push(`/treatment/process2/${processId}`);
      }
    } catch (startError: any) {
      console.error('启动治疗进程失败:', startError);
      
      // 如果是档案不存在的错误，尝试自动修复
      if (startError.response && startError.response.data && 
          startError.response.data.message === '档案不存在') {
        
        console.log('检测到档案不存在错误，尝试自动修复...');
        
        try {
          // 获取现有的档案列表，使用第一个可用的档案
          const recordsResponse = await getRecords({
            page: 1,
            size: 100 // 增加查询数量，确保能找到对应的档案
          });
          
          console.log('获取档案列表响应:', recordsResponse);
          console.log('响应数据结构:', recordsResponse.data);
          
          // 检查不同的响应数据结构
          let records = null;
          if (recordsResponse.data && Array.isArray(recordsResponse.data)) {
            // 如果直接是数组
            records = recordsResponse.data;
          } else if (recordsResponse.data && recordsResponse.data.records && Array.isArray(recordsResponse.data.records)) {
            // 如果有records字段
            records = recordsResponse.data.records;
          } else if (recordsResponse.data && recordsResponse.data.data && Array.isArray(recordsResponse.data.data)) {
            // 如果有data字段
            records = recordsResponse.data.data;
          }
          
          console.log('解析的档案数组:', records);
          
          if (records && records.length > 0) {
            const validRecordId = records[0].id;
            console.log('找到有效档案ID:', validRecordId);
            
            // 使用有效的档案ID重新尝试
            const retryRequest = {
              ...startTreatmentRequest,
              recordId: validRecordId
            };
            
            console.log('使用有效档案ID重试:', retryRequest);
            const retryResponse = await startTreatmentProcess(retryRequest);
            const processId = retryResponse.data.processId;
            
            console.log('重试成功，治疗进程启动:', retryResponse.data);
            MessagePlugin.success(`治疗进程启动成功，进程ID: ${processId}`);
            showTreatmentHeadModal.value = false;
            isConfirming.value = false; // 重置确认状态
            
            // 根据治疗模式决定下一步，传递processId
            if (treatmentMode.value === 'local') {
              router.push(`/treatment/process1/${processId}`);
            } else {
              router.push(`/treatment/process2/${processId}`);
            }
            return; // 成功后返回，不执行下面的错误处理
          } else {
            console.warn('没有找到可用的档案记录');
          }
        } catch (retryError) {
          console.error('自动修复失败:', retryError);
        }
      }
      
      // 如果自动修复失败或其他错误，显示错误信息
      MessagePlugin.error(startError.response?.data?.message || '启动治疗进程失败，请稍后重试');
    }
  } catch (error) {
    console.error('下载治疗参数失败:', error);
    MessagePlugin.error('下载治疗参数失败，请稍后重试');
  } finally {
    // 无论成功还是失败，都重置确认状态
    isConfirming.value = false;
  }
};

// 开始治疗（本地治疗模式）
const startTreatment = async () => {
  const selectedParts = Object.values(treatmentParts.value).filter(part => part.selected);
  
  if (selectedParts.length === 0) {
    MessagePlugin.warning('请至少选择一个治疗部位');
    return;
  }

  try {
    // 设置为本地治疗模式
    treatmentMode.value = 'local';
    
    // 获取部位对应的颜色（按选择顺序分配）
    const getPartColorInfo = () => {
      const colors = ['blue', 'green', 'orange']; // 按选择顺序分配颜色
      const colorMapping: { [key: string]: string } = {};
      
      selectionOrder.value.forEach((partKey, index) => {
        const part = treatmentParts.value[partKey as keyof typeof treatmentParts.value];
        if (part.selected) {
          colorMapping[part.name] = colors[index] || 'blue';
        }
      });
      
      return colorMapping;
    };

    const colorMapping = getPartColorInfo();
    
    // 发送参数到后端，检查治疗头数量
    const treatmentParams = {
      patientId: patientId,
      treatmentMode: 'local' as 'local' | 'takeaway', // 本地治疗模式
      bodyParts: selectedParts.map(part => ({
        name: (part as any).customName || part.name,
        color: colorMapping[part.name] || 'blue', // 添加颜色信息用于控制硬件灯光
        parameters: {
          time: part.time,
          intensity: `${part.intensity}mW/cm²`,
          frequency: `${part.frequency}Hz`,
          depth: part.depth,
          count: part.count
        }
      }))
    };

    console.log('发送本地治疗参数到后端:', treatmentParams);
    
    // 检查治疗头数量
    const response = await checkTreatmentHeadAvailability({
      patientId: patientId as string,
      treatmentMode: treatmentParams.treatmentMode,
      bodyParts: treatmentParams.bodyParts.map((part: any) => ({
        name: part.name,
        color: part.color,
        parameters: {
          time: part.parameters.time,
          intensity: part.parameters.intensity,
          frequency: part.parameters.frequency,
          depth: part.parameters.depth,
          count: parseInt(part.parameters.count)
        }
      }))
    });
    
    // axios拦截器已经提取了data字段，直接使用response.data
    const availabilityData = response.data;
    if (!availabilityData.sufficient) {
      // 治疗头数量不足
      showTreatmentHeadShortageModal.value = true;
    } else {
      // 治疗头数量充足，显示参数下载中弹窗
      showParameterDownloadingModal.value = true;
    }
  } catch (error: any) {
      console.error('开始本地治疗失败:', error);

      // 检查是否是409错误（治疗头资源校验失败）
      if (error.response && error.response.status === 409) {
        validationErrorMessage.value = error.response.data?.message || '治疗头资源不足，请检查设备状态';
        showTreatmentHeadValidationModal.value = true;
      } else {
        MessagePlugin.error('开始本地治疗失败，请稍后重试');
      }
    }
};

// 全局点击监听 - 关闭所有下拉框
const handleGlobalClick = (event: Event) => {
  const target = event.target as HTMLElement;
  
  // 检查点击是否在强度下拉框内
  const isClickInsideIntensity = target.closest('.dropdown-box_1, .intensity-display');
  if (!isClickInsideIntensity) {
    Object.keys(showIntensityDropdown.value).forEach(key => {
      showIntensityDropdown.value[key as keyof typeof showIntensityDropdown.value] = false;
    });
  }
  
  // 检查点击是否在频率下拉框内
  const isClickInsideFrequency = target.closest('.dropdown-group_6, .frequency-display');
  if (!isClickInsideFrequency) {
    Object.keys(showFrequencyDropdown.value).forEach(key => {
      showFrequencyDropdown.value[key as keyof typeof showFrequencyDropdown.value] = false;
    });
  }
  
  // 检查点击是否在治疗贴片下拉框内
  const isClickInsideTreatmentPatch = target.closest('.dropdown-box_2, .treatment-patch-clickable-area');
  if (!isClickInsideTreatmentPatch) {
    Object.keys(showTreatmentPatchDropdown.value).forEach(key => {
      showTreatmentPatchDropdown.value[key as keyof typeof showTreatmentPatchDropdown.value] = false;
    });
  }
  
  // 检查点击是否在时间选择器内
  const isClickInsideTime = target.closest('.time-picker-popup, .time-display');
  if (!isClickInsideTime) {
    Object.keys(showTimeWheel.value).forEach(key => {
      showTimeWheel.value[key as keyof typeof showTimeWheel.value] = false;
    });
  }
};

// 选择深浅（不自动关闭）
const selectDepth = (partKey: string, depth: string) => {
  treatmentParts.value[partKey as keyof typeof treatmentParts.value].depth = depth;
  // 深浅选择不自动关闭下拉框
};

// 选择数量并自动关闭
const selectCount = (partKey: string, count: string) => {
  treatmentParts.value[partKey as keyof typeof treatmentParts.value].count = count;
  showTreatmentPatchDropdown.value[partKey as keyof typeof showTreatmentPatchDropdown.value] = false;
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 获取患者的最新档案ID
const fetchPatientRecordId = async () => {
  try {
    if (!patientId) {
      console.warn('patientId 不存在');
      return;
    }
    
    console.log('当前 patientId:', patientId);
    
    // 处理临时ID的情况
    if (typeof patientId === 'string' && patientId.startsWith('temp_')) {
      console.log('检测到临时ID，准备创建档案记录...');
      
      // 从临时ID中提取时间戳
      const match = patientId.match(/temp_(\d+)/);
      if (match) {
        const timestamp = match[1];
        
        try {
          // 创建档案记录
          const recordData = {
            patientCardId: `CARD_${timestamp.slice(-8)}`, // 使用时间戳后8位作为卡号
            name: '治疗患者', // 默认名称
            gender: '未知',
            age: '成人',
            contactInfo: '',
            recordNumber: `R${timestamp.slice(-8)}`, // 使用时间戳作为档案编号
            diagnosisDescription: '来自候选列表的治疗记录',
            bodyParts: [] // 空的部位统计
          };
          
          console.log('创建档案记录:', recordData);
          const response = await createRecord(recordData);
          
          // 获取创建的档案ID，需要查询刚创建的档案
          const recordsResponse = await getRecords({
            page: 1,
            size: 1,
            cardId: recordData.patientCardId
          });
          
          if (recordsResponse.data && recordsResponse.data.length > 0) {
            recordId.value = recordsResponse.data[0].id;
            console.log('成功创建档案，档案ID:', recordId.value);
          } else {
            // 如果查询失败，使用时间戳的后几位作为备用方案
            recordId.value = parseInt(timestamp.slice(-6));
            console.log('使用备用档案ID:', recordId.value);
          }
          
        } catch (createError: any) {
          console.error('创建档案记录失败:', createError);
          
          // 检查是否是获取患者信息失败
          if (createError.response && createError.response.status === 404) {
            console.error('患者不存在，无法创建档案');
          } else {
            console.error('档案创建过程中发生错误');
          }
          
          // 如果创建失败，使用一个默认的测试ID
          recordId.value = 1;
          console.log('创建失败，使用默认档案ID:', recordId.value);
        }
      } else {
        // 如果解析失败，使用一个默认的测试ID
        recordId.value = 1;
      }
    } else {
      // 处理真实患者ID的情况
      const parsed = parseInt(patientId as string);
      if (!isNaN(parsed)) {
        console.log('检测到真实患者ID:', parsed);
        
        try {
          // 查询该患者相关的档案记录
          const recordsResponse = await getRecords({
            page: 1,
            size: 100 // 增加查询数量，确保能找到对应的档案
          });
          
          console.log('档案查询响应:', recordsResponse);
          
          // 解析响应数据结构
          let records = null;
          if (recordsResponse.data && Array.isArray(recordsResponse.data)) {
            records = recordsResponse.data;
          } else if (recordsResponse.data && recordsResponse.data.records && Array.isArray(recordsResponse.data.records)) {
            records = recordsResponse.data.records;
          } else if (recordsResponse.data && recordsResponse.data.data && Array.isArray(recordsResponse.data.data)) {
            records = recordsResponse.data.data;
          }
          
          if (records && records.length > 0) {
            // 查找该患者ID对应的档案记录
            const patientRecord = records.find((record: any) => record.patientId === parsed);
            
            if (patientRecord) {
              recordId.value = patientRecord.id;
              console.log('找到患者档案记录，档案ID:', recordId.value);
            } else {
              // 如果没找到该患者的档案，为该患者创建一个新的档案记录
              console.log('未找到患者特定档案，为患者创建新档案记录');
              try {
                // 先获取患者的真实信息
                console.log('获取患者真实信息，患者ID:', parsed);
                const patientResponse = await getPatientById(parsed.toString());
                const patientInfo = patientResponse.data.basicInfo;
                
                console.log('患者真实信息:', patientInfo);
                
                const timestamp = Date.now().toString();
                const recordData = {
                  patientCardId: patientInfo.cardId || `PATIENT_${parsed}`,
                  name: patientInfo.name || '未知患者',
                  gender: patientInfo.gender || '未知',
                  age: patientInfo.age || '未知',
                  contactInfo: patientInfo.phone || '',
                  recordNumber: `R${timestamp.slice(-8)}`,
                  diagnosisDescription: `为患者 ${patientInfo.name || parsed} 创建的治疗档案`,
                  bodyParts: []
                };
                
                console.log('使用真实信息创建档案记录:', recordData);
                await createRecord(recordData);
                
                // 重新查询档案记录以获取新创建的档案ID
                const newRecordsResponse = await getRecords({
                  page: 1,
                  size: 1000 // 增加查询数量
                });
                
                let newRecords = null;
                if (newRecordsResponse.data && Array.isArray(newRecordsResponse.data)) {
                  newRecords = newRecordsResponse.data;
                } else if (newRecordsResponse.data && newRecordsResponse.data.records && Array.isArray(newRecordsResponse.data.records)) {
                  newRecords = newRecordsResponse.data.records;
                } else if (newRecordsResponse.data && newRecordsResponse.data.data && Array.isArray(newRecordsResponse.data.data)) {
                  newRecords = newRecordsResponse.data.data;
                }
                
                if (newRecords && newRecords.length > 0) {
                  // 查找刚创建的档案记录
                  const newPatientRecord = newRecords.find((record: any) => 
                    record.patientId === parsed || record.cardId === `PATIENT_${parsed}`
                  );
                  
                  if (newPatientRecord) {
                    recordId.value = newPatientRecord.id;
                    console.log('成功创建并找到新档案记录，档案ID:', recordId.value);
                  } else {
                    // 如果还是找不到，使用第一个可用的档案ID
                    recordId.value = newRecords[0].id;
                    console.log('使用第一个可用档案ID:', recordId.value);
                  }
                } else {
                  console.warn('创建档案后仍无法找到档案记录');
                  recordId.value = parsed; // 使用患者ID作为备用方案
                }
              } catch (createError: any) {
                console.error('创建档案记录失败:', createError);
                
                // 检查是否是获取患者信息失败
                if (createError.response && createError.response.status === 404) {
                  console.error('患者不存在，无法创建档案');
                } else {
                  console.error('档案创建过程中发生错误');
                }
                
                // 如果创建失败，使用第一个可用的档案ID
                if (records && records.length > 0) {
                  recordId.value = records[0].id;
                  console.log('创建失败，使用第一个可用档案ID:', recordId.value);
                } else {
                  recordId.value = parsed;
                }
              }
            }
          } else {
            // 如果没有任何档案记录，使用患者ID作为备用方案
            console.warn('没有找到任何档案记录，使用患者ID作为档案ID');
            recordId.value = parsed;
          }
        } catch (queryError) {
          console.error('查询患者档案失败:', queryError);
          // 查询失败时使用患者ID作为备用方案
          recordId.value = parsed;
        }
      } else {
        recordId.value = 1;
      }
    }
    
    console.log('最终设置档案ID:', recordId.value);
  } catch (error) {
    console.error('获取患者档案失败:', error);
    // 设置默认值以确保功能可以继续
    recordId.value = 1;
  }
};

// 页面加载
onMounted(() => {
  // 获取患者档案ID
  fetchPatientRecordId();
  document.addEventListener('click', handleGlobalClick);
});

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick);
});
</script>

<style scoped>
/* 完全按照蓝湖CSS复刻 */
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1080px;
  background: url('@/assets/images/treatmentsettingsview/img/pskyo5832w8ybrft1eh47bp3j9vyxtk4woa7deeff0-df83-4c92-9f9f-45fda3d64c9b.png') -4px 0px no-repeat;
  background-size: 1936px 1082px;
  width: 1920px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.image-wrapper_1 {
  width: 948px;
  height: 61px;
  margin: 20px 0 0 108px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.image_1 {
  width: 151px;
  height: 61px;
  top:19px;
  cursor: pointer;
}

.title_text {
  height: 36px;
  font-size: 41.67px;
  font-family: MicrosoftYaHei;
  color: #010101;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.section_1 {
  width: 1920px;
  height: 904px;
  margin-top: 95px;
  display: flex;
  flex-direction: row;
}

.image-wrapper_2 {
  height: 892px;
  background: url('@/assets/images/treatmentsettingsview/img/psvuzirtzglxeg1vgwcg2zhhkd17hrkaw7k6e02162e-a04f-4e05-a4b7-da3cb33d59d8.png') 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 12px;
  width: 451px;
  display: flex;
  flex-direction: column;
}

.image_3 {
  width: 182px;
  height: 622px;
  margin: 67px 0 0 59px;
}

.section_2 {
  position: relative;
  width: 1028px;
  height: 822px;
}

.image-wrapper_3 {
  width: 1920px;  /* 扩大容器宽度以包含按钮：1288px - 366px = 922px */
  height: 800px; /* 扩大容器高度以包含按钮：205px + 89px = 294px，向上取整到300px */
  margin: 728px 0 0 366px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
}
/* 治疗按钮样式 */
.treatment-button {
  transition: all 0.2s ease !important;
}

.treatment-button:hover {
  filter: brightness(1.1);
}

.treatment-button:active {
  transform: scale(0.95) !important;
}

.group_1 {
  height: 756px;
  background: url('@/assets/images/treatmentsettingsview/img/框.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 1028px;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
}

/* 表头样式 */
.table-header {
  width: 876px;
  height: 52px;
  margin: 60px 0 0 78px;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-weight: bold;
  color: #323232;
  font-size: 25px;
  font-family: MicrosoftYaHei;
}

.header-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.header-cell:nth-child(1) {
  margin-left: 10px;
}
.header-cell:nth-child(2) {
  margin-left: 30px;
}
.header-cell:nth-child(3) {
  margin-left: 10px;
}
.header-cell:nth-child(4) {
  margin-left: 20px;
}
.header-cell:nth-child(5) {
  margin-left: 20px;
}
.header-cell:nth-child(6) { 
  margin-left: 25px;
}

.header-select {
  width: 51px;
  margin-right: 32px;
}

.header-part {
  width: 80px;
  margin-right: 20px;
}

.header-time {
  width: 100px;
  margin-right: 30px;
}

.header-intensity {
  width: 120px;
  margin-right: 30px;
}

.header-frequency {
  width: 120px;
  margin-right: 30px;
}

.header-patch {
  width: 150px;
}

/* 肩颈部行 - 第一行数据 */
.image-wrapper_4 {
  width: 856px;
  height: 52px;
  margin: 50px 0 0 78px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.image_6 {
  width: 51px;
  height: 50px;
  margin-top: 2px;
  cursor: pointer;
}

/* 腰背部行 */
.image-wrapper_5 {
  width: 857px;
  height: 52px;
  margin: 48px 0 0 77px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.image_12 {
  width: 52px;
  height: 52px;
  cursor: pointer;
}

/* 髋部行 */
.image-wrapper_6 {
  width: 856px;
  height: 50px;
  margin: 38px 0 0 78px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.image_18 {
  width: 51px;
  height: 50px;
  cursor: pointer;
}

/* 上肢行 */
.image-wrapper_7 {
  width: 856px;
  height: 51px;
  margin: 43px 0 0 78px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.image_24 {
  width: 51px;
  height: 51px;
  cursor: pointer;
}

/* 下肢行 */
.image-wrapper_8 {
  width: 856px;
  height: 50px;
  margin: 43px 0 0 78px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.image_30 {
  width: 51px;
  height: 50px;
  cursor: pointer;
}

/* 其他部位行 */
.image-wrapper_9 {
  width: 856px;
  height: 50px;
  margin: 51px 0 78px 78px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.image_36 {
  width: 51px;
  height: 50px;
  cursor: pointer;
}
/* 其他部位文字样式 - 与其他行对齐 */
.param-text_37{
  white-space: nowrap;
  margin: 0 20px 0 32px;
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
  text-align: left;
  display: inline-block;
  align-items: center;
  justify-content: center;
  font-weight: normal;
  width: 86.16px;
  line-height: 155px;
}
/* 参数文字样式 - 按照新UI 25px字体，统一对齐 */
.param-text_13, .param-text_19, .param-text_25, .param-text_31, .param-text_7 {
  width: 100px;
  margin: 0 0 0 52px;
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
  text-align: center;
  display: inline-block;
  align-items: center;
  justify-content: center;
  font-weight: normal;
  cursor: pointer;
  white-space: nowrap;
  line-height: 155px;
}

/* 时间选择器容器 - 统一对齐 */
.time-selector_8, .time-selector_14, .time-selector_20, .time-selector_26, .time-selector_32, .time-selector_38 {
  width: 120px;
  margin: 0 0 0 20px;
  position: relative;
}

/* 强度下拉框 - 统一对齐 */
.param-select_9, .param-select_15, .param-select_21, .param-select_27, .param-select_33, .param-select_39 {
  width: 180px;
  margin: 0 0 0 20px;
}

/* 频率下拉框 - 统一对齐 */
.param-select_10, .param-select_16, .param-select_22, .param-select_28, .param-select_34, .param-select_40 {
  width: 140px;
  margin: 0 0 0 20px;
}

.param-select_38 {
  width: 120px;
  margin: 0 0 0 17px;
}

/* 治疗头容器 - 统一对齐 */
.treatment-container_11, .treatment-container_17, .treatment-container_23, .treatment-container_29, .treatment-container_35, .treatment-container_41 {
  display: flex;
  gap: 8px;
  margin: 0 0 0 20px;
  width: 150px;
}

.depth-select, .count-select {
  font-size: 20px;
  height: 40px;
}

.depth-select {
  width: 80px;
}

.count-select {
  width: 52px;
}

/* 其他部位输入框 */
.param-input_37 {
  width: 100px;
  
  margin: 0 0 0 0px;
  font-size: 20px;
  height: 40px;
}

.image-wrapper_10 {
  height: 892px;
  background: url('@/assets/images/treatmentsettingsview/img/ps7rmr9jmz80684y7c43peq4rgyr66tpnsoecc6742b-0aa4-4b97-9ab3-0102a21fa71c.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 451px;
  margin: 12px 0 0 -10px;
  display: flex;
  flex-direction: column;
}

.image_42 {
  width: 182px;
  height: 622px;
  margin: 67px 0 0 217px;
}

/* TDesign组件样式覆盖 - 20px字体，无边框 */
:deep(.t-select) {
  --td-comp-size-s: 40px;
  border: none !important;
}

:deep(.t-select .t-input) {
  font-size: 25px !important;
  height: 40px;
  line-height: 40px;
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  color: #505050 !important;
}

:deep(.t-input) {
  font-size: 25px !important;
  height: 40px;
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  color: #505050 !important;
}

:deep(.t-select .t-input__inner) {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  font-size: 25px !important;
  color: #505050 !important;
}

:deep(.t-input__inner) {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
  font-size: 25px !important;
  color: #505050 !important;
}

/* 下拉选择框选项文字 - 25px */
:deep(.t-select-option) {
  font-size: 25px !important;
  line-height: 25px !important;
  color: #505050 !important;
}

:deep(.t-select-dropdown .t-select-option) {
  font-size: 25px !important;
  line-height: 25px !important;
  color: #505050 !important;
  padding: 8px 16px;
}

:deep(.t-popup .t-select-option) {
  font-size: 25px !important;
  line-height: 25px !important;
  color: #505050 !important;
}

:deep(.t-select-dropdown) {
  font-size: 25px !important;
}

:deep(.t-select-popup .t-select-option) {
  font-size: 25px !important;
  line-height: 25px !important;
  color: #505050 !important;
}

:deep(.t-select__dropdown .t-select-option) {
  font-size: 20px !important;
  line-height: 20px !important;
}

:deep(.t-select-option__content) {
  font-size: 20px !important;
  line-height: 20px !important;
}

/* 图片堆叠样式 */
.body-image-stack {
  position: relative;
  display: inline-block;
}

.body-image-stack .image_3,
.body-image-stack .image_42 {
  position: absolute;
  top: 0;
  left: 0;
}

.body-image-stack .image_3:first-child,
.body-image-stack .image_42:first-child {
  position: relative;
}

/* 底层人体图片样式 */
.base-body-image {
  position: relative !important;
  z-index: 1 !important;
  opacity: 0.8;
}

.body-image-stack .base-body-image:first-child {
  position: relative !important;
}

/* 确保其他图片在底层人体图之上 */
.body-image-stack .image_3:not(.base-body-image),
.body-image-stack .image_42:not(.base-body-image) {
  z-index: 2;
}

/* 人体标识文字样式 */
.body-label {
  position: absolute;
  font-size: 20px;
  font-family: MicrosoftYaHei, sans-serif;
  color: rgba(89, 89, 89, 1);
  z-index: 15;
  pointer-events: none;

}

/* 正面标识 - 位于右边 */
.front-label {
  right: 120px;
  top: 70%;
  transform: translateY(-50%);
}

/* 背面标识 - 位于左边 */
.back-label {
  left: 120px;
  top: 70%;
  transform: translateY(-50%);
}

/* 颜色图标绝对定位样式 */
.color-icon {
  position: absolute;
  width: auto;
  height: auto;
  z-index: 10;
  pointer-events: none;
}

.color-icon img {
  max-width: none;
  max-height: none;
  display: block;
}

.stacked-image {
  opacity: 0.8 !important;
}

/* 颜色滤镜 - 将#F5E4D1转换为目标颜色 */
.color-filter-blue {
  filter: hue-rotate(200deg) saturate(1.5) brightness(0.8);
}

.color-filter-green {
  filter: hue-rotate(90deg) saturate(1.2) brightness(0.9);
}

.color-filter-orange {
  filter: hue-rotate(30deg) saturate(1.3) brightness(1.1);
}

/* 治疗贴片点击区域 - 扩大点击范围 */
.treatment-patch-clickable-area {
  width: 100%;
  min-height: 45px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 5px;
}

.treatment-patch-clickable-area:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

.treatment-patch-clickable-area-wider {
  min-width: 140px;
}

/* 左右排列的下拉框内容 */
.dropdown-content-horizontal {
  display: flex;
  flex-direction: row;
  gap: 25px;
  padding: 5px 0;
}

.depth-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-left: -20px;
}

.count-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-size: 20px;
  color: rgba(80, 80, 80, 1);
  font-family: Adobe Heiti Std R;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
}

.count-options-vertical {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 两列布局样式 */
.count-options-two-columns {
  display: flex;
  flex-direction: row;
  gap: 2px;
}

.count-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.count-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 3px 5px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.count-option:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

/* 复选按钮样式 - 替代单选按钮 */
.checkbox-btn {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  border: 2px solid #ccc;
  background-color: #f8f8f8;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.checkbox-btn.active {
  background-color: #4a90e2;
  border-color: #4a90e2;
}

.checkbox-btn.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
}

.depth-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.depth-option:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

.depth-option span:last-child {
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
}

.count-option span:last-child {
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
}

/* 时间显示样式 */
.time-display {
  width: 120px;
  height: 40px;
  background-color: transparent;
  border: none;
  font-size: 25px;
  color: #505050;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.time-display:hover {
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 5px;
}

/* 时间弹出网格样式 - 完全展开 */
.time-grid-popup {
  position: absolute;
  z-index: 1000;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
  width: 240px;
  height: 200px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 2px solid #4a90e2;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-top: 200px;
  /*整个框体位置上移200px*/
  
}

.time-cell {
  width: 32px;
  height: 28px;
  background-color: #f8f8f8;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e0e0e0;
}

.time-cell:hover {
  background-color: #e6f3ff;
  border-color: #4a90e2;
  transform: scale(1.05);
}

.time-cell.active {
  background-color: #4a90e2;
  color: white;
  border-color: #4a90e2;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
}

/* 治疗头选择弹窗样式 */
.treatment-head-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 1920px;
  height: 1080px;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

.treatment-head-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1920px;
  height: 1080px;
  background: transparent;
  pointer-events: none;
}

/* 复刻蓝湖治疗头选择界面样式 - 主窗体 */
.treatment-head-container .th-modal-window {
  position: absolute;
  top: 81px;
  left: 534px;
  height: 918px;
  background: url('/lanhu_html/lanhu_zhiliaotouxuanze/img/pseo0e3wj102jq5uijbqe7k2w60lz7r3wlcc742694-a832-4a0c-abda-911963efe8fd.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 877px;
  pointer-events: auto;
}

/* 所有内部元素可点击 */
.treatment-head-container img,
.treatment-head-container .th-row-1,
.treatment-head-container .th-row-2,
.treatment-head-container .th-row-3,
.treatment-head-container .th-row-4,
.treatment-head-container .th-row-5,
.treatment-head-container .th-row-6,
.treatment-head-container .th-row-7,
.treatment-head-container .th-row-8,
.treatment-head-container .th-row-9,
.treatment-head-container .th-row-10 {
  pointer-events: auto;
}

.treatment-head-container .th-row-1 {
  width: 223px;
  height: 11px;
  margin: 104px 0 0 313px;
}

.treatment-head-container .th-item-1 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-2 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-2 {
  width: 223px;
  height: 11px;
  margin: 28px 0 0 313px;
}

.treatment-head-container .th-item-3 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-4 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-3 {
  width: 223px;
  height: 11px;
  margin: 34px 0 0 313px;
}

.treatment-head-container .th-item-5 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-6 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-4 {
  width: 223px;
  height: 11px;
  margin: 31px 0 0 313px;
}

.treatment-head-container .th-item-7 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-8 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-5 {
  width: 223px;
  height: 11px;
  margin: 33px 0 0 313px;
}

.treatment-head-container .th-item-9 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-10 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-6 {
  width: 223px;
  height: 11px;
  margin: 161px 0 0 313px;
}

.treatment-head-container .th-item-11 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-12 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-7 {
  width: 223px;
  height: 11px;
  margin: 28px 0 0 313px;
}

.treatment-head-container .th-item-13 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-14 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-8 {
  width: 223px;
  height: 11px;
  margin: 34px 0 0 313px;
}

.treatment-head-container .th-item-15 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-16 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-9 {
  width: 223px;
  height: 11px;
  margin: 31px 0 0 313px;
}

.treatment-head-container .th-item-17 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-18 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-row-10 {
  width: 223px;
  height: 11px;
  margin: 33px 0 291px 313px;
}

.treatment-head-container .th-item-19 {
  width: 79px;
  height: 11px;
}

.treatment-head-container .th-item-20 {
  width: 79px;
  height: 11px;
}



.treatment-head-container .th-confirm-btn {
  position: absolute;
  top: 947px;
  left: 847px;
  width: 251px;
  height: 102px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.treatment-head-container .th-confirm-btn:hover {
  transform: scale(1.05);
}

/* 治疗头深度区分样式 */
.treatment-head-container .th-depth-section {
  margin-bottom: 0;
}

/* 卡槽容器叠加样式 */
.treatment-head-container .th-slot-container {
  position: relative;
  display: inline-block;
}

.treatment-head-container .th-slot-bg {
  width: 79px;
  height: 11px;
  display: block;
}

.treatment-head-container .th-head-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: auto;
  height: auto;
  object-fit: contain;
  z-index: 2;
}

/* 时间选择器 - 中间不透明向两端自然消散 */
.time-picker-popup {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  /*background: linear-gradient(to bottom, 
    rgba(86, 176, 175, 0) 0%, 
    rgba(86, 176, 175, 0.2) 30%,
    rgba(86, 176, 175, 0.8) 50%, 
    rgba(86, 176, 175, 0.2) 70%,
    rgba(86, 176, 175, 0) 100%);*/
  min-width: 140px; 
  margin-top: -155px;
  padding: 8px 8px 16px 8px;
  overflow: visible;
  background-color: rgba(255, 255, 255, 0.95);
  border: 2px solid #CEECEB;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.time-picker-container {
  display: flex;
  flex-direction: column;
  padding: 4px;
  cursor: grab;
  user-select: none;
}

.time-picker-container:active {
  cursor: grabbing;
}

.time-picker-item {
  padding: 10px 16px;
  font-size: 25px;
  color: #505050;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  border-radius: 6px;
  margin: 2px 0;
  background-color: transparent;
  border: none;
}

.time-picker-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 当前选中的时间 - 用框体标注 */
.time-picker-item.current {
  background-color: rgba(230, 243, 255, 0.95);
  border: 2px solid #CEECEB;
  font-weight: bold;
  color: #075058;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.time-picker-item.current:hover {
  background-color: rgba(208, 232, 255, 0.95);
}

/* 相邻时间 - 透明无边框 */
.time-picker-item.near {
  color: #333;
  opacity: 0.9;
  font-size: 15px;
  font-weight: 500;
  background-color: transparent;
  border: none;
}

.time-picker-item.near:hover {
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 1;
}

/* 远的时间 - 透明无边框 */
.time-picker-item.far {
  color: #888;
  opacity: 0.6;
  font-size: 14px;
  background-color: transparent;
  border: none;
}

.time-picker-item.far:hover {
  background-color: rgba(255, 255, 255, 0.15);
  opacity: 0.8;
}

/* 强度选择器样式更新 */
.intensity-selector_9, .intensity-selector_15, .intensity-selector_21, 
.intensity-selector_27, .intensity-selector_33, .intensity-selector_39 {
  width: 156px;
  margin: 0 0 0 48px;
  position: relative;
}

.intensity-display {
  width: 100%;
  height: 45px;
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
  text-align: center;
  line-height: 45px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.intensity-display:hover {
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 5px;
}

/* 下拉框样式 - 使用真实背景图片 */
.dropdown-box_1 {
  position: absolute;
  top: 45px;
  left: 0;
  width: 156px;
  min-height: 158px;
  background-image: url('@/assets/images/交互界面/参数设置/参数设置/强度下拉框.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 8px;
  padding: 10px 33px;
  /*box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);*/
  z-index: 1001;
}

.dropdown-item {
  width: 102px;
  height: 38px;
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
  text-align: center;
  line-height: 38px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 5px;
  margin: 5px 0;
}

.dropdown-item:hover {
  background-color: rgb(212, 231, 230);
}

.dropdown-item.active {
  background-color: rgba(194, 231, 230, 0.8);
  color: #505050;
}

/* 频率选择器样式更新 */
.frequency-selector_10, .frequency-selector_16, .frequency-selector_22, 
.frequency-selector_28, .frequency-selector_34 {
  width: 159px;
  text-align: center;
  margin: 0 0 0 25px;
  position: relative;
}
.frequency-selector_40{
  width: 169px;
  text-align: center;
  margin: 0 0 0 61px;
  position: relative;
}
.frequency-display {
  width: 100%;
  height: 40px;
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
  text-align: center;
  line-height: 45px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.frequency-display:hover {
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 5px;
}

/* 下拉框样式 - 脉冲频率背景 */
.dropdown-group_6 {
  position: absolute;
  top: 45px;
  left: 0;
  width: 159px;
  min-height: 115px;
  background-image: url('@/assets/images/交互界面/参数设置/参数设置/脉冲频率下拉框.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 8px;
  padding: 11px 22px;
 
  z-index: 1001;
}

/* 治疗贴片容器样式更新 */
.treatment-patch-container_11, .treatment-patch-container_17, .treatment-patch-container_23, 
.treatment-patch-container_29, .treatment-patch-container_35 {
  width: 192px;
  margin: 0 0 0 17px;
  position: relative;
}
.treatment-patch-container_41{
  width: 192px;
  margin: 0 0 0 31px;
  position: relative;
}
.treatment-patch-display {
  width: auto;
  min-width: 80px;
  height: 24px;
  font-size: 25px;
  color: #505050;
  font-family: PingFangSC-Regular, TimesNewRomanPSMT;
  text-align: center;
  line-height: 45px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 63px;
  padding: 0 10px;
  white-space: nowrap;
}

.treatment-patch-display-wider {
  min-width: 120px;
}

.treatment-patch-display:hover {
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 5px;
}

/* 下拉框样式 - 治疗贴片背景 */
.dropdown-box_2 {
  position: absolute;
  top: 0px;
  left: 0;

  background-image: url('@/assets/images/交互界面/参数设置/参数设置/参数设置贴片数量.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  border-radius: 8px;
  padding: 18px 31px;
  /*box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);*/
  z-index: 1001;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  width: 131px;
  height: 24px;
  margin-bottom: 23px;
  text-align: center;
}

.dropdown-header span {
  font-size: 25px;
  color: #505050;
  font-family: MicrosoftYaHei;
  text-align: center;
  line-height: 24px;
}

.dropdown-content {
  display: flex;
  flex-direction: column;
  gap: 13px;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 33px;
}

.depth-option {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.radio-btn {
  width: 21px;
  height: 21px;
  border-radius: 50%;
  border: 2px solid #ccc;
  background-color: #f8f8f8;
  transition: all 0.2s ease;
}

.radio-btn.active {
  background-color: #4a90e2;
  border-color: #4a90e2;
  position: relative;
}

.radio-btn.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
}

.count-options {
  display: flex;
  gap: 14px;
}

.count-btn {
  width: 21px;
  height: 21px;
  font-size: 25px;
  color: rgba(80, 80, 80, 1);
  font-family: Adobe Heiti Std R;
  text-align: center;
  line-height: 21px;
  cursor: pointer;
  border: 2px solid #ccc;
  border-radius: 3px;
  background-color: #f8f8f8;
  transition: all 0.2s ease;
}

.count-btn.active {
  background-color: #4a90e2;
  border-color: #4a90e2;
  color: white;
}

/* Flexbox 布局工具类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style> 
# 🚀 FREEBONE医疗系统 - 完整部署指南

## 📋 系统概述

本系统已完整实现前后端与硬件设备的连通，支持：
- ✅ WebSocket硬件通信
- ✅ 6种硬件指令完整支持
- ✅ 治疗头指示灯控制
- ✅ 实时状态同步
- ✅ 前端硬件API集成

## 🛠️ 环境要求

### 后端环境
- Java 17+
- Gradle 7.0+
- MySQL 8.0+

### 前端环境
- Node.js 16+
- npm 8+

### 硬件环境
- WebSocket硬件控制板
- 地址：ws://**************:6123

## 🚀 快速部署

### 1. 后端部署

```bash
# 进入后端目录
cd BoneSys

# 运行完整系统测试
./test-complete-system.bat

# 或者手动执行
./gradlew clean
./gradlew compileJava
./gradlew test --tests WebSocketHardwareTest
./gradlew bootRun
```

### 2. 前端部署

```bash
# 进入前端目录
cd Bone_Vue

# 运行前端启动脚本
./start-frontend.bat

# 或者手动执行
npm install
npm run dev
```

## 🔧 硬件通信配置

### WebSocket配置
```yaml
# application.yml
hardware:
  websocket:
    url: ws://**************:6123
    timeout: 5000
    reconnect:
      enabled: true
      interval: 5000
```

### 支持的硬件指令

| 指令 | 功能 | 状态 |
|------|------|------|
| TRZI | 查询治疗头信息 | ✅ 完成 |
| TWSC | 点亮治疗头指示灯 | ✅ 完成 |
| TWSN | 关闭治疗头指示灯 | ✅ 完成 |
| TWSDT | 发送治疗参数（不启动） | ✅ 完成 |
| TWZS | 发送治疗参数并启动治疗 | ✅ 完成 |
| TWZO | 关闭治疗头 | ✅ 完成 |

## 🧪 系统测试

### 硬件通信测试
```bash
./gradlew test --tests WebSocketHardwareTest
```

### 指示灯控制测试
```bash
./gradlew test --tests IndicatorLightControlTest
```

### 完整流程测试
```bash
./gradlew test --tests WebSocketHardwareTest.testCompleteWorkflow
```

## 🌐 访问地址

- **前端界面**: http://localhost:5173
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html

## 🔍 功能验证

### 1. 硬件连接验证
- 访问前端治疗头管理页面
- 检查治疗头状态同步
- 验证WebSocket连接状态

### 2. 指示灯控制验证
- 进入参数设置页面
- 点击"生成推荐治疗头"
- 观察指示灯点亮效果
- 关闭弹窗验证指示灯关闭

### 3. 治疗流程验证
- 创建患者档案
- 设置治疗参数
- 启动治疗流程
- 验证硬件指令发送

## 🚨 故障排除

### 硬件连接问题
```bash
# 检查WebSocket连接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" ws://**************:6123
```

### 编译错误
```bash
# 清理重建
./gradlew clean build
```

### 前端启动问题
```bash
# 清理node_modules
rm -rf node_modules package-lock.json
npm install
```

## 📝 开发说明

### 硬件服务架构
- `HardwareService`: 统一硬件操作接口
- `WebSocketHardwareService`: WebSocket通信实现
- `HardwareCommandParser`: 指令构建和解析
- `TreatmentHeadSyncService`: 定时状态同步

### 前端硬件集成
- `src/api/hardware.ts`: 硬件API接口
- `TreatmentSettingsView.vue`: 指示灯控制集成
- 自动指示灯控制逻辑

## ✅ 部署检查清单

- [ ] Java环境配置正确
- [ ] MySQL数据库运行正常
- [ ] Node.js环境配置正确
- [ ] 硬件设备网络连通
- [ ] WebSocket地址配置正确
- [ ] 所有测试用例通过
- [ ] 前后端服务正常启动
- [ ] 硬件指令响应正常

## 🎯 下一步

系统已完整部署，可以开始：
1. 创建患者档案
2. 设置治疗参数
3. 执行治疗流程
4. 监控硬件状态
5. 管理治疗数据

---

**注意**: 确保硬件设备已连接到网络并可通过WebSocket访问。如有问题，请检查网络配置和防火墙设置。
